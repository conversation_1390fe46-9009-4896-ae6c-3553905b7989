# Environment Variables Template
# This file shows all required environment variables for the project
# 
# For local development: Copy to .env.local
# For production: Set these in your deployment platform (Vercel, Netlify, etc.)

# Supabase Configuration
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key-here

# AI Configuration
VITE_GEMINI_API_KEY=your-gemini-api-key-here

# Note: All environment variables that need to be accessible in the browser
# must be prefixed with VITE_ when using Vite as the build tool
