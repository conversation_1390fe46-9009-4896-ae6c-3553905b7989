import { log, LogLevel } from '../utils/logger';

/**
 * Network quality levels
 */
export enum NetworkQuality {
  UNKNOWN = 'unknown',
  OFFLINE = 'offline',
  POOR = 'poor',
  FAIR = 'fair',
  GOOD = 'good',
  EXCELLENT = 'excellent'
}

/**
 * Network information and status
 */
export interface NetworkStatus {
  quality: NetworkQuality;
  online: boolean;
  downlink?: number; // Mbps
  rtt?: number; // ms (round-trip time)
  effectiveType?: 'slow-2g' | '2g' | '3g' | '4g';
  saveData?: boolean;
  lastChecked: number; // timestamp
}

/**
 * Network quality thresholds
 */
const NETWORK_QUALITY_THRESHOLDS = {
  // RTT thresholds in milliseconds
  rtt: {
    poor: 500, // > 500ms is poor
    fair: 200, // > 200ms is fair
    good: 100, // > 100ms is good, <= 100ms is excellent
  },
  // Downlink thresholds in Mbps
  downlink: {
    poor: 0.5, // < 0.5 Mbps is poor
    fair: 1.0, // < 1.0 Mbps is fair
    good: 2.0, // < 2.0 Mbps is good, >= 2.0 Mbps is excellent
  }
};

/**
 * Service for monitoring network quality and connection status
 */
export class NetworkService {
  private static instance: NetworkService;
  private status: NetworkStatus;
  private listeners: Set<(status: NetworkStatus) => void> = new Set();
  private checkInterval: number | null = null;
  private pingEndpoint = '/api/ping'; // Fallback ping endpoint

  private constructor() {
    log(LogLevel.INFO, '[NetworkService] Initializing...');
    
    // Initialize with default values
    this.status = {
      quality: NetworkQuality.UNKNOWN,
      online: navigator.onLine,
      lastChecked: Date.now()
    };

    // Set up event listeners for online/offline events
    window.addEventListener('online', this.handleOnlineStatusChange.bind(this));
    window.addEventListener('offline', this.handleOnlineStatusChange.bind(this));
    
    // Initial network check
    this.checkNetworkQuality();
  }

  public static getInstance(): NetworkService {
    if (!NetworkService.instance) {
      NetworkService.instance = new NetworkService();
    }
    return NetworkService.instance;
  }

  /**
   * Starts periodic network quality monitoring
   * @param intervalMs Interval between checks in milliseconds (default: 30000)
   */
  public startMonitoring(intervalMs: number = 30000): void {
    if (this.checkInterval !== null) {
      this.stopMonitoring();
    }
    
    log(LogLevel.INFO, `[NetworkService] Starting network monitoring every ${intervalMs}ms`);
    this.checkInterval = window.setInterval(() => {
      this.checkNetworkQuality();
    }, intervalMs);
  }

  /**
   * Stops periodic network quality monitoring
   */
  public stopMonitoring(): void {
    if (this.checkInterval !== null) {
      window.clearInterval(this.checkInterval);
      this.checkInterval = null;
      log(LogLevel.INFO, '[NetworkService] Stopped network monitoring');
    }
  }

  /**
   * Gets the current network status
   */
  public getNetworkStatus(): NetworkStatus {
    return { ...this.status };
  }

  /**
   * Adds a listener for network status changes
   * @param listener Function to call when network status changes
   */
  public addStatusListener(listener: (status: NetworkStatus) => void): void {
    this.listeners.add(listener);
  }

  /**
   * Removes a previously added listener
   * @param listener The listener function to remove
   */
  public removeStatusListener(listener: (status: NetworkStatus) => void): void {
    this.listeners.delete(listener);
  }

  /**
   * Manually triggers a network quality check
   */
  public async checkNetworkQuality(): Promise<NetworkStatus> {
    try {
      // Update online status
      this.status.online = navigator.onLine;
      
      if (!this.status.online) {
        this.updateQuality(NetworkQuality.OFFLINE);
        return this.notifyListeners();
      }

      // Try to use the Network Information API if available
      this.checkNetworkInformationAPI();
      
      // Perform a ping test to measure actual response time
      await this.performPingTest();
      
      // Determine overall quality based on collected metrics
      this.determineOverallQuality();
      
      // Update timestamp
      this.status.lastChecked = Date.now();
      
      return this.notifyListeners();
    } catch (error) {
      log(LogLevel.ERROR, '[NetworkService] Error checking network quality:', error);
      return this.status;
    }
  }

  /**
   * Checks if the browser supports the Network Information API
   */
  public supportsNetworkInformationAPI(): boolean {
    return 'connection' in navigator && navigator['connection'] !== undefined;
  }

  /**
   * Sets the endpoint to use for ping tests
   * @param endpoint The URL to ping
   */
  public setPingEndpoint(endpoint: string): void {
    this.pingEndpoint = endpoint;
  }

  /**
   * Handles changes to online/offline status
   */
  private handleOnlineStatusChange(): void {
    const wasOnline = this.status.online;
    this.status.online = navigator.onLine;
    
    if (wasOnline !== this.status.online) {
      log(LogLevel.INFO, `[NetworkService] Connection status changed: ${this.status.online ? 'online' : 'offline'}`);
      
      if (this.status.online) {
        // If we're back online, check quality
        this.checkNetworkQuality();
      } else {
        // If we're offline, update quality and notify
        this.updateQuality(NetworkQuality.OFFLINE);
        this.notifyListeners();
      }
    }
  }

  /**
   * Checks network information using the Network Information API
   */
  private checkNetworkInformationAPI(): void {
    if (!this.supportsNetworkInformationAPI()) {
      return;
    }

    const connection = navigator['connection'];
    
    if (connection) {
      // Update network information from the API
      this.status.downlink = connection.downlink;
      this.status.rtt = connection.rtt;
      this.status.effectiveType = connection.effectiveType;
      this.status.saveData = connection.saveData;
      
      log(LogLevel.DEBUG, '[NetworkService] Network Information API data:', {
        downlink: this.status.downlink,
        rtt: this.status.rtt,
        effectiveType: this.status.effectiveType,
        saveData: this.status.saveData
      });
    }
  }

  /**
   * Performs a ping test to measure actual response time
   */
  private async performPingTest(): Promise<void> {
    try {
      const startTime = Date.now();
      
      // Add a cache-busting parameter to prevent caching
      const pingUrl = `${this.pingEndpoint}?_=${startTime}`;
      
      // Use fetch with a timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout
      
      await fetch(pingUrl, { 
        method: 'HEAD',
        cache: 'no-store',
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      const endTime = Date.now();
      const pingTime = endTime - startTime;
      
      // Update RTT based on ping test
      this.status.rtt = pingTime;
      
      log(LogLevel.DEBUG, `[NetworkService] Ping test result: ${pingTime}ms`);
    } catch (error) {
      log(LogLevel.WARN, '[NetworkService] Ping test failed:', error);
      // If ping fails but we're still online, assume poor connection
      if (this.status.online) {
        this.status.rtt = 1000; // Assume high latency
      }
    }
  }

  /**
   * Determines the overall network quality based on collected metrics
   */
  private determineOverallQuality(): void {
    if (!this.status.online) {
      this.updateQuality(NetworkQuality.OFFLINE);
      return;
    }

    // Determine quality based on RTT (round-trip time)
    let qualityFromRtt = NetworkQuality.UNKNOWN;
    if (this.status.rtt !== undefined) {
      if (this.status.rtt > NETWORK_QUALITY_THRESHOLDS.rtt.poor) {
        qualityFromRtt = NetworkQuality.POOR;
      } else if (this.status.rtt > NETWORK_QUALITY_THRESHOLDS.rtt.fair) {
        qualityFromRtt = NetworkQuality.FAIR;
      } else if (this.status.rtt > NETWORK_QUALITY_THRESHOLDS.rtt.good) {
        qualityFromRtt = NetworkQuality.GOOD;
      } else {
        qualityFromRtt = NetworkQuality.EXCELLENT;
      }
    }

    // Determine quality based on downlink speed
    let qualityFromDownlink = NetworkQuality.UNKNOWN;
    if (this.status.downlink !== undefined) {
      if (this.status.downlink < NETWORK_QUALITY_THRESHOLDS.downlink.poor) {
        qualityFromDownlink = NetworkQuality.POOR;
      } else if (this.status.downlink < NETWORK_QUALITY_THRESHOLDS.downlink.fair) {
        qualityFromDownlink = NetworkQuality.FAIR;
      } else if (this.status.downlink < NETWORK_QUALITY_THRESHOLDS.downlink.good) {
        qualityFromDownlink = NetworkQuality.GOOD;
      } else {
        qualityFromDownlink = NetworkQuality.EXCELLENT;
      }
    }

    // Determine quality based on effective connection type
    let qualityFromEffectiveType = NetworkQuality.UNKNOWN;
    if (this.status.effectiveType) {
      switch (this.status.effectiveType) {
        case 'slow-2g':
          qualityFromEffectiveType = NetworkQuality.POOR;
          break;
        case '2g':
          qualityFromEffectiveType = NetworkQuality.POOR;
          break;
        case '3g':
          qualityFromEffectiveType = NetworkQuality.FAIR;
          break;
        case '4g':
          qualityFromEffectiveType = NetworkQuality.EXCELLENT;
          break;
      }
    }

    // Choose the worst quality as the overall quality
    const qualities = [
      qualityFromRtt, 
      qualityFromDownlink, 
      qualityFromEffectiveType
    ].filter(q => q !== NetworkQuality.UNKNOWN);

    if (qualities.length === 0) {
      // If we couldn't determine quality but we're online, assume FAIR
      this.updateQuality(NetworkQuality.FAIR);
      return;
    }

    // Order of quality from worst to best
    const qualityOrder = [
      NetworkQuality.OFFLINE,
      NetworkQuality.POOR,
      NetworkQuality.FAIR,
      NetworkQuality.GOOD,
      NetworkQuality.EXCELLENT
    ];

    // Find the worst quality
    let worstQualityIndex = Number.MAX_SAFE_INTEGER;
    for (const quality of qualities) {
      const index = qualityOrder.indexOf(quality);
      if (index !== -1 && index < worstQualityIndex) {
        worstQualityIndex = index;
      }
    }

    this.updateQuality(qualityOrder[worstQualityIndex]);
  }

  /**
   * Updates the network quality and logs changes
   */
  private updateQuality(quality: NetworkQuality): void {
    if (this.status.quality !== quality) {
      log(LogLevel.INFO, `[NetworkService] Network quality changed: ${this.status.quality} -> ${quality}`);
      this.status.quality = quality;
    }
  }

  /**
   * Notifies all listeners of the current network status
   */
  private notifyListeners(): NetworkStatus {
    for (const listener of this.listeners) {
      try {
        listener({ ...this.status });
      } catch (error) {
        log(LogLevel.ERROR, '[NetworkService] Error in network status listener:', error);
      }
    }
    return { ...this.status };
  }
}

// Export a singleton instance for use across the application
export const networkService = NetworkService.getInstance();