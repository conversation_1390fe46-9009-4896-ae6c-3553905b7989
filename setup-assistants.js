#!/usr/bin/env node

/**
 * Complete Assistant Setup Script for AI Chat Full Stack
 * This script handles both schema creation and data migration for the assistants feature
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables from frontend/.env.local
function loadEnvLocal() {
    const envPath = path.join(__dirname, 'frontend', '.env.local');

    if (!fs.existsSync(envPath)) {
        console.error('❌ Error: .env.local file not found');
        console.error('Please create frontend/.env.local file with:');
        console.error('VITE_SUPABASE_URL=https://your-project.supabase.co');
        console.error('SUPABASE_SERVICE_KEY=your-service-role-key');
        console.error('');
        console.error('⚠️  Note: You need to add the SERVICE ROLE key');
        console.error('   (not the anon key you already have)');
        console.error('');
        console.error(`Expected location: ${envPath}`);
        process.exit(1);
    }

    const envContent = fs.readFileSync(envPath, 'utf8');
    const envVars = {};

    envContent.split('\n').forEach(line => {
        const trimmed = line.trim();
        if (trimmed && !trimmed.startsWith('#')) {
            const [key, ...valueParts] = trimmed.split('=');
            if (key && valueParts.length > 0) {
                envVars[key.trim()] = valueParts.join('=').trim().replace(/^["']|["']$/g, '');
            }
        }
    });

    return envVars;
}

// Load configuration
const envVars = loadEnvLocal();

// Try different variable name patterns
const SUPABASE_URL = envVars.SUPABASE_URL ||
                     envVars.VITE_SUPABASE_URL ||
                     process.env.SUPABASE_URL ||
                     process.env.VITE_SUPABASE_URL;

const SUPABASE_SERVICE_KEY = envVars.SUPABASE_SERVICE_KEY ||
                            envVars.VITE_SUPABASE_SERVICE_KEY ||
                            envVars.SUPABASE_SERVICE_ROLE_KEY ||
                            process.env.SUPABASE_SERVICE_KEY ||
                            process.env.VITE_SUPABASE_SERVICE_KEY ||
                            process.env.SUPABASE_SERVICE_ROLE_KEY;

// Validate environment variables
if (!SUPABASE_URL) {
    console.error('❌ Error: Missing Supabase URL');
    console.error('Please add to frontend/.env.local:');
    console.error('VITE_SUPABASE_URL=https://your-project.supabase.co');
    console.error('(or SUPABASE_URL=https://your-project.supabase.co)');
    process.exit(1);
}

if (!SUPABASE_SERVICE_KEY) {
    console.error('❌ Error: Missing Supabase Service Key');
    console.error('Please add to frontend/.env.local:');
    console.error('VITE_SUPABASE_SERVICE_KEY=your-service-role-key');
    console.error('(or SUPABASE_SERVICE_KEY=your-service-role-key)');
    console.error('');
    console.error('⚠️  Note: You need the SERVICE ROLE key (not the anon key)');
    console.error('   The anon key (VITE_SUPABASE_ANON_KEY) is for frontend use only.');
    console.error('   The service key bypasses RLS and can create/modify tables.');
    console.error('');
    console.error('   Find your service key in Supabase Dashboard:');
    console.error('   Project Settings → API → service_role key');
    process.exit(1);
}

console.log('✅ Found Supabase URL:', SUPABASE_URL);
console.log('✅ Found Service Key:', SUPABASE_SERVICE_KEY.substring(0, 20) + '...');

// Initialize Supabase client with service role key (bypasses RLS)
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY, {
    auth: {
        autoRefreshToken: false,
        persistSession: false
    }
});

// Default system assistants
const SYSTEM_ASSISTANTS = [
    {
        user_id: null, // System assistants have no owner
        emoji: '🤖',
        name: 'Default Assistant',
        description: 'A helpful AI assistant for general tasks.',
        instructions: 'You are a helpful and friendly AI assistant. Be concise, accurate, and supportive in your responses.',
        parameters: { temperature: 0.7, top_p: 1.0 },
        is_system: true,
        access_level: 'free'
    },
    {
        user_id: null,
        emoji: '🧠',
        name: 'Therapist Assistant',
        description: 'A compassionate AI assistant specialized in providing psychological support and guidance.',
        instructions: 'You are a compassionate and empathetic AI therapist. Provide supportive, non-judgmental responses that help users explore their feelings and thoughts. Always encourage professional help when appropriate and never provide medical diagnoses.',
        parameters: { temperature: 0.8, top_p: 0.9 },
        is_system: true,
        access_level: 'free'
    },
    {
        user_id: null,
        emoji: '💼',
        name: 'Business Advisor',
        description: 'An AI assistant focused on business strategy, entrepreneurship, and professional development.',
        instructions: 'You are a knowledgeable business advisor with expertise in strategy, entrepreneurship, and professional development. Provide practical, actionable advice while being direct and results-oriented.',
        parameters: { temperature: 0.6, top_p: 0.8 },
        is_system: true,
        access_level: 'premium' // Example: Premium assistant for future
    },
    {
        user_id: null,
        emoji: '🎓',
        name: 'Learning Tutor',
        description: 'An educational AI assistant that helps with learning and academic support.',
        instructions: 'You are a patient and encouraging tutor. Break down complex topics into understandable parts, provide examples, and adapt your teaching style to help users learn effectively. Always encourage curiosity and critical thinking.',
        parameters: { temperature: 0.7, top_p: 0.85 },
        is_system: true,
        access_level: 'free'
    },
    {
        user_id: null,
        emoji: '💻',
        name: 'Code Assistant',
        description: 'A technical AI assistant specialized in programming and software development.',
        instructions: 'You are an expert software developer and coding mentor. Provide clear, well-commented code examples, explain programming concepts thoroughly, and help debug issues. Focus on best practices and clean code principles.',
        parameters: { temperature: 0.5, top_p: 0.8 },
        is_system: true,
        access_level: 'premium' // Example: Premium assistant for future
    }
];

async function checkTableExists(tableName) {
    try {
        const { error } = await supabase
            .from(tableName)
            .select('count')
            .limit(1);

        return !error;
    } catch (error) {
        return false;
    }
}

async function createAssistantsTable() {
    console.log('🔧 Creating assistants table...');
    
    // Check if table already exists
    const tableExists = await checkTableExists('assistants');
    if (tableExists) {
        console.log('ℹ️  Assistants table already exists, checking structure...');
        
        // Check if we need to migrate from old schema
        try {
            const { error } = await supabase
                .from('assistants')
                .select('access_level')
                .limit(1);

            if (error && error.message.includes('access_level')) {
                console.log('🔄 Detected old assistants table schema, migrating...');
                await migrateToNewSchema();
                return;
            }
        } catch (error) {
            // access_level column doesn't exist, need to migrate
            console.log('🔄 Migrating to new schema with access_level...');
            await migrateToNewSchema();
            return;
        }
        
        return;
    }

    console.log('⚠️  Manual SQL execution required');
    console.log('');
    console.log('Please run the following SQL in your Supabase SQL Editor:');
    console.log('(Dashboard → SQL Editor → New Query)');
    console.log('');
    console.log('--- Copy and paste this SQL ---');
    console.log(`
-- Create assistants table with user_id for future tiered access
CREATE TABLE IF NOT EXISTS assistants (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    emoji TEXT DEFAULT '🤖',
    name TEXT NOT NULL,
    description TEXT,
    instructions TEXT NOT NULL,
    parameters JSONB DEFAULT '{}',
    is_system BOOLEAN DEFAULT FALSE,
    access_level TEXT DEFAULT 'free' CHECK (access_level IN ('free', 'premium', 'enterprise')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_assistants_user_id ON assistants(user_id);
CREATE INDEX IF NOT EXISTS idx_assistants_is_system ON assistants(is_system);
CREATE INDEX IF NOT EXISTS idx_assistants_access_level ON assistants(access_level);
CREATE INDEX IF NOT EXISTS idx_assistants_updated_at ON assistants(updated_at DESC);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_assistants_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS update_assistants_updated_at ON assistants;
CREATE TRIGGER update_assistants_updated_at
    BEFORE UPDATE ON assistants
    FOR EACH ROW EXECUTE FUNCTION update_assistants_updated_at();

-- Enable RLS and create policies
ALTER TABLE assistants ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view all system assistants" ON assistants;
DROP POLICY IF EXISTS "Users can view own custom assistants" ON assistants;

-- Users can view all system assistants
CREATE POLICY "Users can view all system assistants" ON assistants
    FOR SELECT USING (is_system = true);

-- Users can view own custom assistants
CREATE POLICY "Users can view own custom assistants" ON assistants
    FOR SELECT USING (auth.uid() = user_id);
    `);
    console.log('--- End of SQL ---');
    console.log('');

    // Wait for user confirmation
    const readline = require('readline');
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });

    await new Promise((resolve) => {
        rl.question('Have you executed the SQL above? (y/n): ', (answer) => {
            rl.close();
            if (answer.toLowerCase() !== 'y' && answer.toLowerCase() !== 'yes') {
                console.log('❌ Setup cancelled. Please run the SQL first.');
                process.exit(1);
            }
            resolve();
        });
    });

    console.log('✅ Assistants table created successfully');
}

async function migrateToNewSchema() {
    console.log('🔄 Migrating to new schema with access levels...');

    // First, backup existing assistants
    const { data: existingAssistants, error: fetchError } = await supabase
        .from('assistants')
        .select('*');

    if (fetchError) {
        console.error('❌ Error fetching existing assistants:', fetchError.message);
        throw fetchError;
    }

    console.log(`📦 Found ${existingAssistants?.length || 0} existing assistants to backup`);

    // Save backup
    if (existingAssistants && existingAssistants.length > 0) {
        const backupFile = `assistants-backup-${Date.now()}.json`;
        fs.writeFileSync(backupFile, JSON.stringify(existingAssistants, null, 2));
        console.log(`💾 Backed up existing assistants to ${backupFile}`);
    }

    console.log('⚠️  Manual SQL execution required for migration');
    console.log('');
    console.log('Please run the following SQL in your Supabase SQL Editor:');
    console.log('');
    console.log('--- Migration SQL ---');
    console.log(`
DROP TABLE IF EXISTS assistants CASCADE;

CREATE TABLE assistants (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    emoji TEXT DEFAULT '🤖',
    name TEXT NOT NULL,
    description TEXT,
    instructions TEXT NOT NULL,
    parameters JSONB DEFAULT '{}',
    is_system BOOLEAN DEFAULT FALSE,
    access_level TEXT DEFAULT 'free' CHECK (access_level IN ('free', 'premium', 'enterprise')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_assistants_user_id ON assistants(user_id);
CREATE INDEX idx_assistants_is_system ON assistants(is_system);
CREATE INDEX idx_assistants_access_level ON assistants(access_level);
CREATE INDEX idx_assistants_updated_at ON assistants(updated_at DESC);

CREATE OR REPLACE FUNCTION update_assistants_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_assistants_updated_at
    BEFORE UPDATE ON assistants
    FOR EACH ROW EXECUTE FUNCTION update_assistants_updated_at();

ALTER TABLE assistants ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view all system assistants" ON assistants
    FOR SELECT USING (is_system = true);

CREATE POLICY "Users can view own custom assistants" ON assistants
    FOR SELECT USING (auth.uid() = user_id);
    `);
    console.log('--- End Migration SQL ---');
    console.log('');

    const readline = require('readline');
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });

    await new Promise((resolve) => {
        rl.question('Have you executed the migration SQL above? (y/n): ', (answer) => {
            rl.close();
            if (answer.toLowerCase() !== 'y' && answer.toLowerCase() !== 'yes') {
                console.log('❌ Migration cancelled. Please run the SQL first.');
                process.exit(1);
            }
            resolve();
        });
    });

    console.log('✅ Successfully migrated to new schema with access levels');
}

async function insertSystemAssistants() {
    console.log('🤖 Inserting system assistants...');
    
    // Check if system assistants already exist
    const { data: existing, error: checkError } = await supabase
        .from('assistants')
        .select('name')
        .eq('is_system', true);
    
    if (checkError) {
        console.error('❌ Error checking existing assistants:', checkError.message);
        throw checkError;
    }

    if (existing && existing.length > 0) {
        console.log(`ℹ️  Found ${existing.length} existing system assistants, skipping insertion`);
        return;
    }

    // Insert system assistants
    const { error: insertError } = await supabase
        .from('assistants')
        .insert(SYSTEM_ASSISTANTS);

    if (insertError) {
        console.error('❌ Error inserting system assistants:', insertError.message);
        throw insertError;
    }

    console.log(`✅ Successfully inserted ${SYSTEM_ASSISTANTS.length} system assistants`);
}

async function verifySetup() {
    console.log('🔍 Verifying assistant setup...');
    
    const { data: assistants, error } = await supabase
        .from('assistants')
        .select('*')
        .order('is_system', { ascending: false })
        .order('name');

    if (error) {
        console.error('❌ Error verifying setup:', error.message);
        throw error;
    }

    console.log('✅ Setup verification:');
    console.log(`  - Total assistants: ${assistants?.length || 0}`);
    
    const systemAssistants = assistants?.filter(a => a.is_system) || [];
    const customAssistants = assistants?.filter(a => !a.is_system) || [];
    
    console.log(`  - System assistants: ${systemAssistants.length}`);
    console.log(`  - Custom assistants: ${customAssistants.length}`);
    
    systemAssistants.forEach(assistant => {
        console.log(`    • ${assistant.emoji} ${assistant.name}`);
    });

    return true;
}

async function main() {
    console.log('🚀 Starting complete assistant setup...\n');

    try {
        // Step 1: Create or migrate assistants table
        await createAssistantsTable();

        // Step 2: Insert system assistants
        await insertSystemAssistants();

        // Step 3: Verify setup
        await verifySetup();

        console.log('\n🎉 Assistant setup completed successfully!');
        console.log('\n📋 What was accomplished:');
        console.log('✅ Created/migrated assistants table with global access');
        console.log('✅ Inserted 5 default psychological assistants');
        console.log('✅ Set up proper indexes and permissions');
        console.log('✅ All assistants are now available to all users');
        
        console.log('\n🔧 Next steps:');
        console.log('1. Test the assistant selection in the frontend sidebar');
        console.log('2. Verify that all users can see the same assistants');
        console.log('3. Test chat functionality with different assistants');
        console.log('4. The assistants are now global - no user-specific creation/editing');

    } catch (error) {
        console.error('❌ Setup failed:', error.message);
        process.exit(1);
    }
}

// Run the setup
if (require.main === module) {
    main();
}

module.exports = { main };
