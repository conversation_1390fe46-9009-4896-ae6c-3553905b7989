{"useTranslation Hook": {"prefix": "usetrans", "body": ["const { t } = useTranslation();"], "description": "Add useTranslation hook"}, "Translation Function": {"prefix": "trans", "body": ["t('${1:translationKey}')"], "description": "Add translation function call"}, "Translation with Interpolation": {"prefix": "transi", "body": ["t('${1:translationKey}', { ${2:variable}: ${3:value} })"], "description": "Add translation with interpolation"}, "Add Translation Key": {"prefix": "addtrans", "body": ["// Run: npm run translation:add \"${1:key}\" \"${2:English value}\""], "description": "Comment with command to add translation key"}}