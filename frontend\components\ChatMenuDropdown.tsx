import React, { useState, useRef, useEffect } from 'react';
import ReactDOM from 'react-dom';
import { useTranslation } from 'react-i18next';
import { PencilIcon, TrashIcon, DotsVerticalIcon } from '../constants';

interface ChatMenuDropdownProps {
  chatId: string;
  chatTitle: string;
  onEdit: (chatId: string, newTitle: string) => void;
  onDelete: (chatId: string) => void;
  isActive: boolean;
}

const ChatMenuDropdown: React.FC<ChatMenuDropdownProps> = ({
  chatId,
  chatTitle,
  onEdit,
  onDelete,
  isActive
}) => {
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editTitle, setEditTitle] = useState(chatTitle);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const [menuPosition, setMenuPosition] = useState<{ top: number; left: number; width: number } | null>(null);

  useEffect(() => {
    if (isOpen && buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      setMenuPosition({
        top: rect.bottom + window.scrollY,
        left: rect.right - 192 + window.scrollX, // 192px is the menu width
        width: rect.width,
      });
    }
  }, [isOpen]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // If click is outside both the button and the dropdown
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
        setIsEditing(false);
        setEditTitle(chatTitle);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, chatTitle]);

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditing]);

  const handleMenuClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsOpen(!isOpen);
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsEditing(true);
    setIsOpen(false);
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsOpen(false);
    
    if (window.confirm(t('confirmDeleteChat', 'Are you sure you want to delete this chat? This action cannot be undone.'))) {
      onDelete(chatId);
    }
  };

  const handleSaveEdit = () => {
    if (editTitle.trim() && editTitle.trim() !== chatTitle) {
      onEdit(chatId, editTitle.trim());
    }
    setIsEditing(false);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSaveEdit();
    } else if (e.key === 'Escape') {
      setIsEditing(false);
      setEditTitle(chatTitle);
    }
  };

  const handleInputClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  if (isEditing) {
    return (
      <div className="flex-1 mr-2">
        <input
          ref={inputRef}
          type="text"
          value={editTitle}
          onChange={(e) => setEditTitle(e.target.value)}
          onBlur={handleSaveEdit}
          onKeyDown={handleKeyPress}
          onClick={handleInputClick}
          className="w-full px-2 py-1 text-sm bg-white dark:bg-slate-700 border border-blue-500 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
        />
      </div>
    );
  }

  return (
    <div className="relative">
      <button
        ref={buttonRef}
        onClick={handleMenuClick}
        className={`p-1 rounded hover:bg-slate-200 dark:hover:bg-slate-600 transition-colors ${
          isActive ? 'text-blue-600 dark:text-blue-400' : 'text-slate-500 dark:text-slate-400'
        }`}
        title={t('chatOptions', 'Chat options')}
      >
        <DotsVerticalIcon className="w-4 h-4" />
      </button>

      {isOpen && menuPosition && ReactDOM.createPortal(
        <div
          ref={dropdownRef}
          className="absolute z-[9999] w-48 bg-white dark:bg-slate-700 rounded-md shadow-lg ring-1 ring-black ring-opacity-5"
          style={{
            top: menuPosition.top,
            left: menuPosition.left,
            position: 'absolute',
          }}
        >
          <div className="py-1">
            <button
              onClick={handleEdit}
              className="flex items-center w-full px-4 py-2 text-sm text-slate-700 dark:text-slate-200 hover:bg-slate-100 dark:hover:bg-slate-600"
            >
              <PencilIcon className="w-4 h-4 mr-3" />
              {t('editChatTitle', 'Edit title')}
            </button>
            <button
              onClick={handleDelete}
              className="flex items-center w-full px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20"
            >
              <TrashIcon className="w-4 h-4 mr-3" />
              {t('deleteChat', 'Delete chat')}
            </button>
          </div>
        </div>,
        document.body
      )}
    </div>
  );
};

export default ChatMenuDropdown;