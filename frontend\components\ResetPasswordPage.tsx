import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { supabase } from "../lib/supabase";

const ResetPasswordPage: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  // Support both query string, hash fragment, and sessionStorage for Supabase token
  let accessToken: string | null = null;
  let type: string | null = null;
  let refreshToken: string | null = null;

  // 1. Try to get from URL hash
  if (typeof window !== "undefined" && window.location.hash) {
    const hash = window.location.hash.substring(1);
    const params = new URLSearchParams(hash);
    accessToken = params.get("access_token");
    type = params.get("type");
    refreshToken = params.get("refresh_token");
  }

  // 2. If not found, try sessionStorage
  if (!accessToken || !type) {
    accessToken = sessionStorage.getItem("supabase_recovery_access_token");
    type = sessionStorage.getItem("supabase_recovery_type");
    refreshToken = sessionStorage.getItem("supabase_recovery_refresh_token");
  }

  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [sessionReady, setSessionReady] = useState(false);

  // Initialize session with recovery tokens
  useEffect(() => {
    const initializeSession = async () => {
      console.log("[ResetPasswordPage] Component mounted");
      console.log("[ResetPasswordPage] Current URL:", window.location.href);
      console.log("[ResetPasswordPage] URL hash:", window.location.hash);
      console.log("[ResetPasswordPage] accessToken:", !!accessToken);
      console.log("[ResetPasswordPage] type:", type);

      // Give Supabase's detectSessionInUrl time to process the URL
      console.log("[ResetPasswordPage] Waiting for Supabase to process URL session...");
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Check if Supabase has established a session
      try {
        const { data: { session } } = await supabase.auth.getSession();
        if (session && session.user) {
          console.log("[ResetPasswordPage] Session found via Supabase detectSessionInUrl:", session.user.email);
          setSessionReady(true);
          return;
        }
      } catch (err) {
        console.error("[ResetPasswordPage] Error checking session:", err);
      }

      // If no session and we have tokens, try manual setup
      if (accessToken && type === "recovery") {
        console.log("[ResetPasswordPage] No automatic session, trying manual setup...");
        try {
          const { data, error } = await supabase.auth.setSession({
            access_token: accessToken,
            refresh_token: refreshToken || ""
          });

          if (error) {
            console.error("[ResetPasswordPage] Manual session setup failed:", error);
            setError("Failed to initialize password reset session. Please try the reset link again.");
          } else {
            console.log("[ResetPasswordPage] Manual session setup successful:", data);
          }
        } catch (err) {
          console.error("[ResetPasswordPage] Exception in manual session setup:", err);
          setError("Failed to initialize password reset session. Please try the reset link again.");
        }
      }

      setSessionReady(true);
    };

    initializeSession();
  }, [accessToken, type, refreshToken]);

  // Backup navigation effect in case direct navigation fails
  useEffect(() => {
    if (success) {
      const timer = setTimeout(() => {
        navigate("/reset-password-success", { replace: true });
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [success, navigate]);

  if (!accessToken || type !== "recovery") {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40 min-h-screen">
        <div className="w-full sm:max-w-md mx-auto my-auto rounded-2xl bg-white dark:bg-slate-800 border-b-2 border-black/10 p-10 relative shadow-xl flex flex-col text-center">
          <div className="text-red-500 font-semibold">{t('invalidPasswordResetToken')}</div>
        </div>
      </div>
    );
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!password || !confirmPassword) {
      setError(t('pleaseFillBothFields'));
      return;
    }
    if (password !== confirmPassword) {
      setError(t('passwordsDoNotMatchSimple'));
      return;
    }

    // If session isn't ready, try to initialize it first
    if (!sessionReady && accessToken && type === "recovery") {
      console.log("[ResetPasswordPage] Session not ready, attempting to initialize...");
      try {
        const { data, error } = await supabase.auth.setSession({
          access_token: accessToken,
          refresh_token: refreshToken || ""
        });

        if (error) {
          console.error("[ResetPasswordPage] Failed to set session:", error);
          setError("Failed to initialize session. Please try the reset link again.");
          return;
        }

        console.log("[ResetPasswordPage] Session set successfully:", data);
        setSessionReady(true);
      } catch (err) {
        console.error("[ResetPasswordPage] Exception setting session:", err);
        setError("Failed to initialize session. Please try the reset link again.");
        return;
      }
    }

    setLoading(true);

    // Add timeout to prevent infinite loading
    const timeoutId = setTimeout(() => {
      setLoading(false);
      setError("Request timed out. Please check your connection and try again.");
    }, 10000); // 10 second timeout

    console.log("[ResetPasswordPage] Starting password update process...");
    console.log("[ResetPasswordPage] Supabase URL:", import.meta.env.VITE_SUPABASE_URL);
    console.log("[ResetPasswordPage] Has access token:", !!accessToken);
    console.log("[ResetPasswordPage] Token type:", type);

    // Ensure we have a valid session before attempting password update
    try {
      const { data: { session: currentSession } } = await supabase.auth.getSession();
      console.log("[ResetPasswordPage] Current session before update:", currentSession ? "exists" : "null");

      if (!currentSession) {
        console.log("[ResetPasswordPage] No session found, attempting to set session with tokens...");

        if (accessToken && type === "recovery") {
          const { data: sessionData, error: sessionError } = await supabase.auth.setSession({
            access_token: accessToken,
            refresh_token: refreshToken || ""
          });

          if (sessionError) {
            clearTimeout(timeoutId);
            setLoading(false);
            setError("Failed to establish session. Please try the reset link again.");
            console.error("[ResetPasswordPage] Session error:", sessionError);
            return;
          }

          console.log("[ResetPasswordPage] Session established:", sessionData);
        } else {
          clearTimeout(timeoutId);
          setLoading(false);
          setError("Invalid reset tokens. Please try the reset link again.");
          return;
        }
      } else {
        console.log("[ResetPasswordPage] Session user:", currentSession.user?.email);
      }
    } catch (sessionCheckError) {
      clearTimeout(timeoutId);
      setLoading(false);
      setError("Failed to verify session. Please try again.");
      console.error("[ResetPasswordPage] Error checking session:", sessionCheckError);
      return;
    }

    try {
      console.log("[ResetPasswordPage] Attempting password update...");
      console.log("[ResetPasswordPage] Current session before updateUser:");

      // Log current session details for debugging
      const { data: { session: debugSession } } = await supabase.auth.getSession();
      console.log("[ResetPasswordPage] Session user:", debugSession?.user?.email);
      console.log("[ResetPasswordPage] Session expires at:", debugSession?.expires_at);
      console.log("[ResetPasswordPage] Access token present:", !!debugSession?.access_token);

      // Try the password update with detailed logging
      console.log("[ResetPasswordPage] Calling supabase.auth.updateUser...");
      const startTime = Date.now();

      // Try direct API approach if SDK method fails
      const updatePromise = supabase.auth.updateUser({ password });
      const timeoutPromise = new Promise<never>((_, reject) =>
        setTimeout(() => {
          const elapsed = Date.now() - startTime;
          console.log(`[ResetPasswordPage] Password update timed out after ${elapsed}ms`);
          console.log("[ResetPasswordPage] Attempting fallback direct API approach...");
          reject(new Error('Password update timeout'));
        }, 8000)
      );

      const result = await Promise.race([updatePromise, timeoutPromise]);
      clearTimeout(timeoutId);

      const elapsed = Date.now() - startTime;
      console.log(`[ResetPasswordPage] Password update completed in ${elapsed}ms`);

      const { error, data } = result;
      console.log("[ResetPasswordPage] supabase.auth.updateUser result:", { error, data });

      if (error) {
        setLoading(false);
        setError(error.message);
        console.log("[ResetPasswordPage] Error from supabase.auth.updateUser:", error.message);
      } else {
        console.log("[ResetPasswordPage] Password reset success! Cleaning up and redirecting...");

        // Clear the recovery token from sessionStorage
        sessionStorage.removeItem("supabase_recovery_access_token");
        sessionStorage.removeItem("supabase_recovery_type");

        // Clear the URL hash to prevent issues
        if (window.location.hash) {
          window.history.replaceState(null, '', window.location.pathname);
        }

        // Ensure the session is properly established with timeout
        try {
          await Promise.race([
            supabase.auth.refreshSession(),
            new Promise((_, reject) => setTimeout(() => reject(new Error('Session refresh timeout')), 5000))
          ]);
        } catch (sessionError) {
          console.warn("[ResetPasswordPage] Session refresh failed, but continuing:", sessionError);
        }

        setLoading(false);
        setSuccess(true);

        // Navigate immediately without waiting for useEffect
        navigate("/reset-password-success", { replace: true });
      }
    } catch (err) {
      clearTimeout(timeoutId);
      console.log("[ResetPasswordPage] Caught error:", err);

      // If it's a timeout error, try direct API approach
      if (err instanceof Error && err.message === 'Password update timeout') {
        console.log("[ResetPasswordPage] Attempting direct API fallback...");
        setError("SDK timeout, trying direct API approach...");

        try {
          // Get current session for the API call
          const { data: { session: currentSession } } = await supabase.auth.getSession();

          if (!currentSession?.access_token) {
            setError("No valid session for direct API call");
            return;
          }

          console.log("[ResetPasswordPage] Making direct API call to update password...");

          // Direct API call to Supabase Auth API
          const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/auth/v1/user`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${currentSession.access_token}`,
              'apikey': import.meta.env.VITE_SUPABASE_ANON_KEY
            },
            body: JSON.stringify({ password })
          });

          console.log("[ResetPasswordPage] Direct API response status:", response.status);

          if (!response.ok) {
            const errorData = await response.text();
            console.log("[ResetPasswordPage] Direct API error:", errorData);
            setError(`Direct API failed: ${response.status} ${response.statusText}`);
            return;
          }

          const result = await response.json();
          console.log("[ResetPasswordPage] Direct API success:", result);

          // Success! Clean up and redirect
          console.log("[ResetPasswordPage] Password reset success via direct API! Cleaning up and redirecting...");

          // Clear the recovery token from sessionStorage
          sessionStorage.removeItem("supabase_recovery_access_token");
          sessionStorage.removeItem("supabase_recovery_type");

          // Clear the URL hash to prevent issues
          if (window.location.hash) {
            window.history.replaceState(null, '', window.location.pathname);
          }

          setLoading(false);
          setSuccess(true);

          // Navigate to success page
          navigate("/reset-password-success", { replace: true });

        } catch (directApiError) {
          console.error("[ResetPasswordPage] Direct API error:", directApiError);
          setError("Direct API call failed: " + (directApiError instanceof Error ? directApiError.message : String(directApiError)));
        }
      } else {
        setError("Unexpected error: " + (err instanceof Error ? err.message : String(err)));
      }
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40 min-h-screen">
      <div className="w-full sm:max-w-md mx-auto my-auto rounded-2xl bg-white dark:bg-slate-800 border-b-2 border-black/10 p-10 relative shadow-xl flex flex-col text-center">
        <div className="mb-6">
          <span className="text-3xl font-bold text-white dark:text-slate-200 bg-gradient-to-br from-[#1A1919] to-[#64646B] rounded-2xl px-4 py-2 inline-block">Z</span>
        </div>
        <div className="mb-4">
          <div className="text-2xl font-bold textGradient bg-gradient-to-r from-[#191a1d] via-[#888888] to-[#191a1d] bg-clip-text text-transparent mb-2">{t('setNewPassword')}</div>
          <div className="text-sm text-gray-500 dark:text-gray-400 font-normal mb-4 mt-2">{t('pleaseEnterNewPassword')}</div>
        </div>
        {!sessionReady && !error && (
          <div className="text-blue-600 text-sm mb-2 font-semibold">
            {t('initializingPasswordReset')}
          </div>
        )}
        {error && <div className="text-red-500 text-sm mb-2">{error}</div>}
        {success && <div className="text-green-600 text-sm mb-2 font-semibold">{t('passwordResetSuccessful')}</div>}
        <form className="flex flex-col gap-4 w-full" onSubmit={handleSubmit}>
          {/* Hidden username field for accessibility */}
          <input type="text" name="username" autoComplete="username" style={{ display: "none" }} tabIndex={-1} />
          <div className="text-left">
            <label className="text-sm font-medium mb-1 block">{t('newPassword')}</label>
            <input
              type="password"
              autoComplete="new-password"
              className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-slate-700 text-slate-900 dark:text-slate-100 placeholder-slate-400 dark:placeholder-slate-500"
              placeholder={t('enterNewPassword')}
              value={password}
              onChange={e => setPassword(e.target.value)}
              disabled={loading || success}
            />
          </div>
          <div className="text-left">
            <label className="text-sm font-medium mb-1 block">{t('confirmPassword')}</label>
            <input
              type="password"
              autoComplete="new-password"
              className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-slate-700 text-slate-900 dark:text-slate-100 placeholder-slate-400 dark:placeholder-slate-500"
              placeholder={t('confirmNewPassword')}
              value={confirmPassword}
              onChange={e => setConfirmPassword(e.target.value)}
              disabled={loading || success}
            />
          </div>
          <button
            type="submit"
            className="ButtonSignIn ButtonCreateAccount button-gradient bg-[#222428] dark:bg-slate-700 hover:bg-opacity-90 dark:hover:bg-slate-600 text-white dark:text-gray-300 dark:hover:text-white w-full rounded-lg font-medium text-sm py-2.5 mt-2"
            disabled={loading || success}
          >
            {loading ? t('setting') : success ? t('passwordSet') : t('setPassword')}
          </button>
        </form>
        {/* Fallback button if redirect does not work */}
        {success && (
          <button
            type="button"
            className="ButtonSignIn ButtonCreateAccount button-gradient bg-[#222428] dark:bg-slate-700 hover:bg-opacity-90 dark:hover:bg-slate-600 text-white dark:text-gray-300 dark:hover:text-white w-full rounded-lg font-medium text-sm py-2.5 mt-2"
            onClick={() => navigate("/reset-password-success", { replace: true })}
          >
            {t('ifNotRedirected')}
          </button>
        )}
      </div>
    </div>
  );
};

export default ResetPasswordPage;