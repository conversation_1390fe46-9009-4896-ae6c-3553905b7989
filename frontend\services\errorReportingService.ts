import { log, LogLevel } from '../utils/logger';
import { networkService } from './networkService';

/**
 * Error severity levels
 */
export enum ErrorSeverity {
  LOW = 'low',       // Minor issues that don't affect functionality
  MEDIUM = 'medium', // Issues that affect some functionality but app still works
  HIGH = 'high',     // Serious issues that significantly impact functionality
  CRITICAL = 'critical' // Fatal errors that prevent core functionality
}

/**
 * Error report structure
 */
export interface ErrorReport {
  id: string;
  timestamp: number;
  message: string;
  stack?: string;
  severity: ErrorSeverity;
  context: Record<string, any>;
  userId?: string;
  sessionId?: string;
  source: string;
  tags: string[];
  metadata: Record<string, any>;
  sent: boolean;
}

/**
 * Configuration options for error reporting
 */
export interface ErrorReportingOptions {
  /** Whether to automatically send reports (default: true) */
  autoSend?: boolean;
  /** Maximum number of reports to keep in storage (default: 100) */
  maxStoredReports?: number;
  /** Storage key for persisting reports (default: 'error_reports') */
  storageKey?: string;
  /** Whether to capture unhandled errors and rejections (default: true) */
  captureUnhandled?: boolean;
  /** Endpoint for sending error reports (default: '/api/error-reporting') */
  reportingEndpoint?: string;
  /** Whether to include stack traces in reports (default: true) */
  includeStackTraces?: boolean;
  /** Whether to persist reports to localStorage (default: true) */
  persistReports?: boolean;
  /** Whether to log reports to console (default: true) */
  logToConsole?: boolean;
  /** Function to determine if an error should be reported */
  shouldReport?: (error: Error, context?: Record<string, any>) => boolean;
}

/**
 * Service for capturing, storing, and reporting errors
 */
export class ErrorReportingService {
  private static instance: ErrorReportingService;
  private reports: ErrorReport[] = [];
  private options: Required<ErrorReportingOptions>;
  private sendingPromise: Promise<void> | null = null;
  private globalContext: Record<string, any> = {};

  private constructor(options: ErrorReportingOptions = {}) {
    // Set default options
    this.options = {
      autoSend: true,
      maxStoredReports: 100,
      storageKey: 'error_reports',
      captureUnhandled: true,
      reportingEndpoint: '/api/error-reporting',
      includeStackTraces: true,
      persistReports: true,
      logToConsole: true,
      shouldReport: () => true,
      ...options
    };

    log(LogLevel.INFO, '[ErrorReportingService] Initializing...');
    
    // Load saved reports if persistence is enabled
    if (this.options.persistReports) {
      this.loadReportsFromStorage();
    }

    // Set up global error handlers if enabled
    if (this.options.captureUnhandled) {
      this.setupGlobalErrorHandlers();
    }
  }

  public static getInstance(options: ErrorReportingOptions = {}): ErrorReportingService {
    if (!ErrorReportingService.instance) {
      ErrorReportingService.instance = new ErrorReportingService(options);
    }
    return ErrorReportingService.instance;
  }

  /**
   * Sets global context data to include with all error reports
   * @param context Context data to include
   */
  public setGlobalContext(context: Record<string, any>): void {
    this.globalContext = { ...context };
  }

  /**
   * Adds data to the global context
   * @param key Context key
   * @param value Context value
   */
  public addToGlobalContext(key: string, value: any): void {
    this.globalContext[key] = value;
  }

  /**
   * Reports an error with optional context
   * @param error The error to report
   * @param context Additional context for the error
   * @param severity Error severity level
   * @param tags Optional tags for categorizing the error
   * @returns The generated error report
   */
  public reportError(
    error: Error | string,
    context: Record<string, any> = {},
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    tags: string[] = []
  ): ErrorReport {
    // Convert string errors to Error objects
    const errorObj = typeof error === 'string' ? new Error(error) : error;
    
    // Check if we should report this error
    if (!this.options.shouldReport(errorObj, context)) {
      return null;
    }

    // Create the error report
    const report: ErrorReport = {
      id: this.generateId(),
      timestamp: Date.now(),
      message: errorObj.message,
      stack: this.options.includeStackTraces ? errorObj.stack : undefined,
      severity,
      context: { ...this.globalContext, ...context },
      source: 'client',
      tags,
      metadata: {
        userAgent: navigator.userAgent,
        url: window.location.href,
        networkStatus: networkService.getNetworkStatus()
      },
      sent: false
    };

    // Add to reports collection
    this.reports.push(report);
    
    // Trim reports if we exceed the maximum
    if (this.reports.length > this.options.maxStoredReports) {
      this.reports = this.reports.slice(-this.options.maxStoredReports);
    }

    // Log to console if enabled
    if (this.options.logToConsole) {
      const logLevel = this.severityToLogLevel(severity);
      log(logLevel, `[ErrorReportingService] Error captured (${severity}):`, errorObj.message, context);
    }

    // Persist reports if enabled
    if (this.options.persistReports) {
      this.saveReportsToStorage();
    }

    // Send report if auto-send is enabled
    if (this.options.autoSend) {
      this.sendReports();
    }

    return report;
  }

  /**
   * Gets all stored error reports
   * @param filter Optional filter function
   * @returns Array of error reports
   */
  public getReports(filter?: (report: ErrorReport) => boolean): ErrorReport[] {
    if (filter) {
      return this.reports.filter(filter);
    }
    return [...this.reports];
  }

  /**
   * Gets unsent error reports
   * @returns Array of unsent error reports
   */
  public getUnsentReports(): ErrorReport[] {
    return this.reports.filter(report => !report.sent);
  }

  /**
   * Clears all stored reports
   */
  public clearReports(): void {
    this.reports = [];
    
    if (this.options.persistReports) {
      this.saveReportsToStorage();
    }
    
    log(LogLevel.INFO, '[ErrorReportingService] All reports cleared');
  }

  /**
   * Sends all unsent error reports to the server
   * @returns Promise that resolves when sending is complete
   */
  public async sendReports(): Promise<void> {
    // If already sending, return the existing promise
    if (this.sendingPromise) {
      return this.sendingPromise;
    }

    const unsentReports = this.getUnsentReports();
    if (unsentReports.length === 0) {
      return Promise.resolve();
    }

    // Check network status
    const networkStatus = networkService.getNetworkStatus();
    if (!networkStatus.online) {
      log(LogLevel.WARN, '[ErrorReportingService] Cannot send reports: offline');
      return Promise.resolve();
    }

    log(LogLevel.INFO, `[ErrorReportingService] Sending ${unsentReports.length} error reports`);

    // Create and store the sending promise
    this.sendingPromise = this.sendReportsBatch(unsentReports)
      .finally(() => {
        this.sendingPromise = null;
      });

    return this.sendingPromise;
  }

  /**
   * Sets up global error handlers for unhandled errors and rejections
   */
  private setupGlobalErrorHandlers(): void {
    // Handle uncaught errors
    window.addEventListener('error', (event) => {
      this.reportError(
        event.error || new Error(event.message),
        {
          type: 'uncaught_error',
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno
        },
        ErrorSeverity.HIGH,
        ['uncaught', 'global']
      );
    });

    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      const error = event.reason instanceof Error 
        ? event.reason 
        : new Error(String(event.reason));
      
      this.reportError(
        error,
        { type: 'unhandled_rejection' },
        ErrorSeverity.HIGH,
        ['unhandled_rejection', 'global']
      );
    });

    log(LogLevel.INFO, '[ErrorReportingService] Global error handlers set up');
  }

  /**
   * Sends a batch of error reports to the server
   */
  private async sendReportsBatch(reports: ErrorReport[]): Promise<void> {
    try {
      const response = await fetch(this.options.reportingEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ reports })
      });

      if (!response.ok) {
        throw new Error(`Server responded with status: ${response.status}`);
      }

      // Mark reports as sent
      for (const report of reports) {
        report.sent = true;
      }

      log(LogLevel.SUCCESS, `[ErrorReportingService] Successfully sent ${reports.length} error reports`);
    } catch (error) {
      log(LogLevel.ERROR, '[ErrorReportingService] Failed to send error reports:', error);
    }

    // Save updated report status
    if (this.options.persistReports) {
      this.saveReportsToStorage();
    }
  }

  /**
   * Saves reports to localStorage
   */
  private saveReportsToStorage(): void {
    try {
      localStorage.setItem(this.options.storageKey, JSON.stringify(this.reports));
    } catch (error) {
      log(LogLevel.ERROR, '[ErrorReportingService] Error saving reports to storage:', error);
    }
  }

  /**
   * Loads reports from localStorage
   */
  private loadReportsFromStorage(): void {
    try {
      const storedReports = localStorage.getItem(this.options.storageKey);
      if (storedReports) {
        this.reports = JSON.parse(storedReports);
        const unsentCount = this.getUnsentReports().length;
        log(LogLevel.INFO, `[ErrorReportingService] Loaded ${this.reports.length} reports from storage (${unsentCount} unsent)`);
      }
    } catch (error) {
      log(LogLevel.ERROR, '[ErrorReportingService] Error loading reports from storage:', error);
      // If there's an error loading, start with an empty reports array
      this.reports = [];
    }
  }

  /**
   * Converts error severity to log level
   */
  private severityToLogLevel(severity: ErrorSeverity): LogLevel {
    switch (severity) {
      case ErrorSeverity.LOW:
        return LogLevel.INFO;
      case ErrorSeverity.MEDIUM:
        return LogLevel.WARN;
      case ErrorSeverity.HIGH:
      case ErrorSeverity.CRITICAL:
        return LogLevel.ERROR;
      default:
        return LogLevel.WARN;
    }
  }

  /**
   * Generates a unique ID for an error report
   */
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  }
}

// Export a singleton instance for use across the application
export const errorReportingService = ErrorReportingService.getInstance();