import { log, LogLevel } from '../utils/logger';
import { networkService, NetworkQuality } from './networkService';

/**
 * Types of operations that can be queued
 */
export enum OperationType {
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
  CUSTOM = 'custom'
}

/**
 * Status of a queued operation
 */
export enum OperationStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

/**
 * Interface for a queued operation
 */
export interface QueuedOperation<T = any> {
  id: string;
  type: OperationType;
  entityType: string;
  entityId?: string;
  data: T;
  status: OperationStatus;
  createdAt: number;
  processedAt?: number;
  error?: string;
  retryCount: number;
  maxRetries: number;
}

/**
 * Interface for operation execution function
 */
export type OperationExecutor<T = any> = (operation: QueuedOperation<T>) => Promise<boolean>;

/**
 * Configuration options for the offline queue
 */
export interface OfflineQueueOptions {
  /** Storage key for persisting the queue (default: 'offline_operations_queue') */
  storageKey?: string;
  /** Maximum number of retry attempts (default: 3) */
  maxRetries?: number;
  /** Whether to automatically process the queue when online (default: true) */
  autoProcess?: boolean;
  /** Interval in milliseconds to check for connectivity and process queue (default: 30000) */
  processIntervalMs?: number;
  /** Whether to persist the queue to localStorage (default: true) */
  persistQueue?: boolean;
}

/**
 * Service for managing operations while offline and syncing when online
 */
export class OfflineQueueService {
  private static instance: OfflineQueueService;
  private queue: QueuedOperation[] = [];
  private executors: Map<string, OperationExecutor> = new Map();
  private processingPromise: Promise<void> | null = null;
  private processInterval: number | null = null;
  private options: Required<OfflineQueueOptions>;
  private isProcessing = false;

  private constructor(options: OfflineQueueOptions = {}) {
    // Set default options
    this.options = {
      storageKey: 'offline_operations_queue',
      maxRetries: 3,
      autoProcess: true,
      processIntervalMs: 30000,
      persistQueue: true,
      ...options
    };

    log(LogLevel.INFO, '[OfflineQueueService] Initializing...');
    
    // Load queue from storage if enabled
    if (this.options.persistQueue) {
      this.loadQueueFromStorage();
    }

    // Set up network status listener
    networkService.addStatusListener(this.handleNetworkStatusChange.bind(this));
    
    // Start automatic processing if enabled
    if (this.options.autoProcess) {
      this.startAutoProcessing();
    }
  }

  public static getInstance(options: OfflineQueueOptions = {}): OfflineQueueService {
    if (!OfflineQueueService.instance) {
      OfflineQueueService.instance = new OfflineQueueService(options);
    }
    return OfflineQueueService.instance;
  }

  /**
   * Registers an executor function for a specific entity type
   * @param entityType The type of entity this executor handles
   * @param executor Function that will process operations for this entity type
   */
  public registerExecutor(entityType: string, executor: OperationExecutor): void {
    this.executors.set(entityType, executor);
    log(LogLevel.INFO, `[OfflineQueueService] Registered executor for ${entityType}`);
  }

  /**
   * Adds an operation to the queue
   * @param operation The operation to queue
   * @returns The queued operation with generated ID
   */
  public enqueue<T>(operation: Omit<QueuedOperation<T>, 'id' | 'status' | 'createdAt' | 'retryCount' | 'maxRetries'>): QueuedOperation<T> {
    const queuedOperation: QueuedOperation<T> = {
      id: this.generateId(),
      status: OperationStatus.PENDING,
      createdAt: Date.now(),
      retryCount: 0,
      maxRetries: this.options.maxRetries,
      ...operation
    };

    this.queue.push(queuedOperation);
    log(LogLevel.INFO, `[OfflineQueueService] Enqueued ${operation.type} operation for ${operation.entityType}`, 
      operation.entityId ? `ID: ${operation.entityId}` : '');
    
    // Persist queue if enabled
    if (this.options.persistQueue) {
      this.saveQueueToStorage();
    }

    // Try to process immediately if we're online
    const networkStatus = networkService.getNetworkStatus();
    if (networkStatus.online && networkStatus.quality !== NetworkQuality.POOR) {
      this.processQueue();
    }

    return queuedOperation;
  }

  /**
   * Gets all operations in the queue
   * @param filter Optional filter function
   * @returns Array of queued operations
   */
  public getQueue(filter?: (op: QueuedOperation) => boolean): QueuedOperation[] {
    if (filter) {
      return this.queue.filter(filter);
    }
    return [...this.queue];
  }

  /**
   * Gets pending operations for a specific entity
   * @param entityType The entity type to filter by
   * @param entityId Optional entity ID to filter by
   * @returns Array of pending operations
   */
  public getPendingOperations(entityType: string, entityId?: string): QueuedOperation[] {
    return this.queue.filter(op => 
      op.status === OperationStatus.PENDING && 
      op.entityType === entityType && 
      (entityId === undefined || op.entityId === entityId)
    );
  }

  /**
   * Removes an operation from the queue
   * @param operationId ID of the operation to remove
   * @returns True if the operation was found and removed
   */
  public removeOperation(operationId: string): boolean {
    const initialLength = this.queue.length;
    this.queue = this.queue.filter(op => op.id !== operationId);
    
    const removed = initialLength > this.queue.length;
    if (removed && this.options.persistQueue) {
      this.saveQueueToStorage();
    }
    
    return removed;
  }

  /**
   * Clears all operations from the queue
   * @param status Optional status to filter which operations to clear
   */
  public clearQueue(status?: OperationStatus): void {
    if (status) {
      this.queue = this.queue.filter(op => op.status !== status);
    } else {
      this.queue = [];
    }
    
    if (this.options.persistQueue) {
      this.saveQueueToStorage();
    }
    
    log(LogLevel.INFO, `[OfflineQueueService] Queue cleared${status ? ` for status: ${status}` : ''}`);
  }

  /**
   * Starts automatic processing of the queue
   */
  public startAutoProcessing(): void {
    if (this.processInterval !== null) {
      this.stopAutoProcessing();
    }
    
    log(LogLevel.INFO, `[OfflineQueueService] Starting auto-processing every ${this.options.processIntervalMs}ms`);
    this.processInterval = window.setInterval(() => {
      const networkStatus = networkService.getNetworkStatus();
      if (networkStatus.online && networkStatus.quality !== NetworkQuality.POOR) {
        this.processQueue();
      }
    }, this.options.processIntervalMs);
  }

  /**
   * Stops automatic processing of the queue
   */
  public stopAutoProcessing(): void {
    if (this.processInterval !== null) {
      window.clearInterval(this.processInterval);
      this.processInterval = null;
      log(LogLevel.INFO, '[OfflineQueueService] Stopped auto-processing');
    }
  }

  /**
   * Manually triggers processing of the queue
   * @returns Promise that resolves when processing is complete
   */
  public async processQueue(): Promise<void> {
    // If already processing, return the existing promise
    if (this.processingPromise) {
      return this.processingPromise;
    }

    // If no pending operations, do nothing
    const pendingOperations = this.queue.filter(op => op.status === OperationStatus.PENDING);
    if (pendingOperations.length === 0) {
      return Promise.resolve();
    }

    // Check network status
    const networkStatus = networkService.getNetworkStatus();
    if (!networkStatus.online) {
      log(LogLevel.WARN, '[OfflineQueueService] Cannot process queue: offline');
      return Promise.resolve();
    }

    log(LogLevel.INFO, `[OfflineQueueService] Processing queue: ${pendingOperations.length} pending operations`);
    this.isProcessing = true;

    // Create and store the processing promise
    this.processingPromise = this.processOperations(pendingOperations)
      .finally(() => {
        this.processingPromise = null;
        this.isProcessing = false;
      });

    return this.processingPromise;
  }

  /**
   * Checks if the queue is currently being processed
   */
  public isQueueProcessing(): boolean {
    return this.isProcessing;
  }

  /**
   * Gets the count of operations in the queue by status
   */
  public getOperationCounts(): Record<OperationStatus, number> {
    const counts = {
      [OperationStatus.PENDING]: 0,
      [OperationStatus.PROCESSING]: 0,
      [OperationStatus.COMPLETED]: 0,
      [OperationStatus.FAILED]: 0
    };
    
    for (const op of this.queue) {
      counts[op.status]++;
    }
    
    return counts;
  }

  /**
   * Handles network status changes
   */
  private handleNetworkStatusChange(status: any): void {
    if (status.online && status.quality !== NetworkQuality.POOR && this.options.autoProcess) {
      log(LogLevel.INFO, '[OfflineQueueService] Network is online, processing queue...');
      this.processQueue();
    }
  }

  /**
   * Processes a batch of operations
   */
  private async processOperations(operations: QueuedOperation[]): Promise<void> {
    // Process operations in sequence to avoid race conditions
    for (const operation of operations) {
      // Skip if not pending
      if (operation.status !== OperationStatus.PENDING) {
        continue;
      }

      // Update status to processing
      operation.status = OperationStatus.PROCESSING;
      if (this.options.persistQueue) {
        this.saveQueueToStorage();
      }

      try {
        // Get the executor for this entity type
        const executor = this.executors.get(operation.entityType);
        if (!executor) {
          throw new Error(`No executor registered for entity type: ${operation.entityType}`);
        }

        // Execute the operation
        const success = await executor(operation);

        if (success) {
          // Operation succeeded
          operation.status = OperationStatus.COMPLETED;
          operation.processedAt = Date.now();
          log(LogLevel.SUCCESS, `[OfflineQueueService] Operation ${operation.id} completed successfully`);
        } else {
          // Operation failed but might be retryable
          if (operation.retryCount < operation.maxRetries) {
            operation.retryCount++;
            operation.status = OperationStatus.PENDING;
            log(LogLevel.WARN, `[OfflineQueueService] Operation ${operation.id} failed, will retry (${operation.retryCount}/${operation.maxRetries})`);
          } else {
            // Max retries reached
            operation.status = OperationStatus.FAILED;
            operation.error = 'Max retries exceeded';
            log(LogLevel.ERROR, `[OfflineQueueService] Operation ${operation.id} failed after ${operation.maxRetries} retries`);
          }
        }
      } catch (error) {
        // Handle execution error
        if (operation.retryCount < operation.maxRetries) {
          operation.retryCount++;
          operation.status = OperationStatus.PENDING;
          operation.error = error instanceof Error ? error.message : String(error);
          log(LogLevel.WARN, `[OfflineQueueService] Error executing operation ${operation.id}:`, error);
          log(LogLevel.INFO, `[OfflineQueueService] Will retry (${operation.retryCount}/${operation.maxRetries})`);
        } else {
          operation.status = OperationStatus.FAILED;
          operation.error = error instanceof Error ? error.message : String(error);
          log(LogLevel.ERROR, `[OfflineQueueService] Operation ${operation.id} failed after ${operation.maxRetries} retries:`, error);
        }
      }

      // Persist queue after each operation
      if (this.options.persistQueue) {
        this.saveQueueToStorage();
      }
    }

    // Clean up completed operations after processing
    this.cleanupCompletedOperations();
  }

  /**
   * Removes completed operations that are older than a certain threshold
   */
  private cleanupCompletedOperations(): void {
    const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);
    const initialLength = this.queue.length;
    
    this.queue = this.queue.filter(op => {
      // Keep all non-completed operations
      if (op.status !== OperationStatus.COMPLETED) {
        return true;
      }
      
      // Remove completed operations older than one day
      return op.processedAt && op.processedAt > oneDayAgo;
    });
    
    const removedCount = initialLength - this.queue.length;
    if (removedCount > 0) {
      log(LogLevel.INFO, `[OfflineQueueService] Cleaned up ${removedCount} completed operations`);
      
      if (this.options.persistQueue) {
        this.saveQueueToStorage();
      }
    }
  }

  /**
   * Saves the current queue to localStorage
   */
  private saveQueueToStorage(): void {
    try {
      localStorage.setItem(this.options.storageKey, JSON.stringify(this.queue));
    } catch (error) {
      log(LogLevel.ERROR, '[OfflineQueueService] Error saving queue to storage:', error);
    }
  }

  /**
   * Loads the queue from localStorage
   */
  private loadQueueFromStorage(): void {
    try {
      const storedQueue = localStorage.getItem(this.options.storageKey);
      if (storedQueue) {
        this.queue = JSON.parse(storedQueue);
        const counts = this.getOperationCounts();
        log(LogLevel.INFO, '[OfflineQueueService] Loaded queue from storage:', counts);
      }
    } catch (error) {
      log(LogLevel.ERROR, '[OfflineQueueService] Error loading queue from storage:', error);
      // If there's an error loading, start with an empty queue
      this.queue = [];
    }
  }

  /**
   * Generates a unique ID for an operation
   */
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  }
}

// Export a singleton instance for use across the application
export const offlineQueueService = OfflineQueueService.getInstance();