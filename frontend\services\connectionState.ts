// Connection state manager for global app use
// Usage: import { connectionState } from './connectionState'

export type ConnectionStatus = 'connected' | 'connecting' | 'disconnected' | 'error';

class ConnectionState {
  private status: ConnectionStatus = 'connecting';
  private listeners: Set<(status: ConnectionStatus) => void> = new Set();
  private error: string | null = null;

  getStatus() {
    return this.status;
  }

  getError() {
    return this.error;
  }

  setStatus(status: ConnectionStatus, error: string | null = null) {
    if (this.status !== status || this.error !== error) {
      this.status = status;
      this.error = error;
      this.listeners.forEach((cb) => cb(this.status));
    }
  }

  subscribe(cb: (status: ConnectionStatus) => void) {
    this.listeners.add(cb);
    // Immediately notify
    cb(this.status);
    return () => this.listeners.delete(cb);
  }
}

export const connectionState = new ConnectionState();
