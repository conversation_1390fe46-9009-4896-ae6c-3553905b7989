Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFB740, 0007FFFFA640) msys-2.0.dll+0x1FE8E
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210286019, 0007FFFFB5F8, 0007FFFFB740, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB740  000210068E24 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA20  00021006A225 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE22870000 ntdll.dll
7FFE22160000 KERNEL32.DLL
7FFE20240000 KERNELBASE.dll
7FFE20820000 USER32.dll
7FFE20650000 win32u.dll
7FFE20BF0000 GDI32.dll
7FFE20120000 gdi32full.dll
7FFE1FE20000 msvcp_win.dll
7FFE1FEC0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFE20770000 advapi32.dll
7FFE20680000 msvcrt.dll
7FFE21270000 sechost.dll
7FFE20AD0000 RPCRT4.dll
7FFE1F3F0000 CRYPTBASE.DLL
7FFE200A0000 bcryptPrimitives.dll
7FFE20730000 IMM32.DLL
