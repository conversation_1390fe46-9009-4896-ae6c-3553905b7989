/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./App.tsx",
    "./index.tsx",
    "./components/**/*.{js,ts,jsx,tsx}"
  ],
  theme: {
    extend: {
      colors: {
        bubble: {
          light: '#4E4E4E',
          dark: '#D4D4D4',
        },
        header: {
          light: '#101828',
          dark: '#FFFFFF',
        },
        codeblock: {
          text: '#E6E7E9',
          bg: '#1D283A',
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
  ],
}

