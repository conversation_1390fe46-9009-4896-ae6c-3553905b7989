---
name: "Translation Sync"
description: "Automatically sync missing translation keys when translation files are modified"
trigger: "file_save"
filePattern: "frontend/locales/*/translation.json"
---

# Translation Sync Hook

This hook automatically syncs missing translation keys whenever you save a translation file.

## Actions

1. Check for missing translation keys across all languages
2. Add placeholder translations for missing keys
3. Report translation coverage

## Usage

This hook runs automatically when you save any translation file in `frontend/locales/*/translation.json`.

You can also manually run translation commands:
- `npm run translation:check` - Check translation coverage
- `npm run translation:sync` - Sync missing keys with placeholders
- `npm run translation:add <key> <value>` - Add a new translation key

## Example

When you add a new key to English translations:
```json
{
  "newFeature": "New Feature"
}
```

The hook will automatically add placeholder translations to other languages:
```json
{
  "newFeature": "[RU] New Feature"
}
```

Then you can replace the placeholder with the proper translation.