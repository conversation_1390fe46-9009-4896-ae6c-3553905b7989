import React, { useState, useEffect } from 'react';
import { PASCard } from './PASCard';

// Example assessment data; in production, fetch from API or context
const PAS_ASSESSMENTS = [
  {
    id: '6hn',
    title: '6 Human Needs',
    description: 'Discover your core human needs and how they shape your life.',
    icon: '/assets/6hn-icon.png',
  },
  {
    id: 'mbti',
    title: 'MBTI',
    description: 'Myers-Briggs Type Indicator personality assessment.',
    icon: '/assets/mbti-icon.png',
  },
  {
    id: 'disc',
    title: 'DiSC',
    description: 'DiSC behavioral assessment for teams and individuals.',
    icon: '/assets/disc-icon.png',
  },
  // Add more as needed
];

interface PASSelectorProps {
  onSelectAssessment: (assessmentId: string) => void;
  selectedAssessmentId?: string;
}

export const PASSelector: React.FC<PASSelectorProps> = ({ onSelectAssessment, selectedAssessmentId }) => {
  // In production, fetch assessments from API and handle loading/error states
  const [assessments, setAssessments] = useState(PAS_ASSESSMENTS);

  useEffect(() => {
    // TODO: fetch assessments from backend if needed
  }, []);

  return (
    <div className="flex flex-col items-center border-2 border-dashed border-blue-400 rounded-lg p-4 mb-4 bg-blue-50 dark:bg-blue-900">
      <h2 className="text-xl font-bold text-blue-700 dark:text-blue-200 mb-2">Personality Assessments</h2>
      <div className="flex flex-row flex-wrap justify-center gap-2">
        {assessments.map((a) => (
          <PASCard
            key={a.id}
            id={a.id}
            title={a.title}
            description={a.description}
            icon={a.icon}
            onSelect={onSelectAssessment}
          />
        ))}
      </div>
    </div>
  );
};
