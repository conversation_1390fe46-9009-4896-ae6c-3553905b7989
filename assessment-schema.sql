-- PAS Assessment System Schema Migration

-- Assessments table
CREATE TABLE assessments (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    icon VARCHAR(255),
    type VARCHAR(50), -- e.g., '6HN', 'MBTI'
    is_active BOOLEAN DEFAULT TRUE
);

-- Assessment questions table
CREATE TABLE assessment_questions (
    id SERIAL PRIMARY KEY,
    assessment_id INTEGER REFERENCES assessments(id),
    category VARCHAR(100),
    question_text TEXT NOT NULL,
    follow_up_text TEXT,
    "order" INTEGER
);

-- Quick answers table
CREATE TABLE assessment_quick_answers (
    id SERIAL PRIMARY KEY,
    question_id INTEGER REFERENCES assessment_questions(id),
    answer_text TEXT NOT NULL,
    score_value INTEGER,
    "order" INTEGER
);


-- Extend chat_sessions table for assessment and team support
ALTER TABLE chat_sessions
    ADD COLUMN IF NOT EXISTS assessment_id INTEGER REFERENCES assessments(id),
    ADD COLUMN IF NOT EXISTS assessment_title VARCHAR(255),
    ADD COLUMN IF NOT EXISTS assessment_icon VARCHAR(255),
    ADD COLUMN IF NOT EXISTS team_id INTEGER;


-- Extend messages table for assessment support
ALTER TABLE messages
    ADD COLUMN IF NOT EXISTS question_id INTEGER REFERENCES assessment_questions(id),
    ADD COLUMN IF NOT EXISTS quick_answer_id INTEGER REFERENCES assessment_quick_answers(id),
    ADD COLUMN IF NOT EXISTS score INTEGER,
    ADD COLUMN IF NOT EXISTS is_assessment BOOLEAN DEFAULT FALSE,
    ADD COLUMN IF NOT EXISTS is_free_text BOOLEAN DEFAULT FALSE;

CREATE TABLE assessment_results (
    id SERIAL PRIMARY KEY,
    session_id UUID REFERENCES chat_sessions(id),
    assessment_id INTEGER REFERENCES assessments(id),
    result_json JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Teams table (optional, for team assessments)
CREATE TABLE teams (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Team members table (optional)
CREATE TABLE team_members (
    id SERIAL PRIMARY KEY,
    team_id INTEGER REFERENCES teams(id),
    user_id INTEGER,
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
