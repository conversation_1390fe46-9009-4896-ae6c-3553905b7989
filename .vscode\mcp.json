{"inputs": [{"type": "promptString", "id": "********************************************", "description": "Supabase personal access token", "password": true}], "servers": {"supabase": {"command": "cmd", "args": ["/c", "npx", "-y", "@supabase/mcp-server-supabase@latest", "--read-only", "--project-ref=duuwyfgfcidcqbjmogjg"], "env": {"SUPABASE_ACCESS_TOKEN": "${input:********************************************}"}}}}