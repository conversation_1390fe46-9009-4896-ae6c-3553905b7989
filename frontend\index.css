/* Unordered list marker color for AI bubbles (dark theme) */
.dark .message-content ul li::marker {
  color: #D4D4D4;
}
/* Unordered list marker color for AI bubbles (light theme) */
.message-content ul li::marker {
  color: #4E4E4E;
}
/* Inline code text color as regular bubble text */
.message-content code:not(pre code) {
  color: #4E4E4E;
}
.dark .message-content code:not(pre code) {
  color: #D4D4D4;
}
/* Inline code with language-markdown text color as regular bubble text */
.message-content code.language-markdown:not(pre code) {
  color: #4E4E4E;
}
.dark .message-content code.language-markdown:not(pre code) {
  color: #D4D4D4;
}
/* Table text color for AI bubbles */
.message-content table,
.message-content td,
.message-content th {
  color: #4E4E4E;
}
.dark .message-content table,
.dark .message-content td,
.dark .message-content th {
  color: #D4D4D4 !important;
}

/* List text color for AI bubbles */
.message-content ul,
.message-content ol,
.message-content ul li,
.message-content ol li {
  color: #4E4E4E;
}
.dark .message-content ul,
.dark .message-content ol,
.dark .message-content ul li,
.dark .message-content ol li {
  color: #D4D4D4;
}
/* Paragraph text color for AI bubbles */
.message-content p {
  color: #4E4E4E;
}
.dark .message-content p {
  color: #D4D4D4;
}
@tailwind base;
@tailwind components;
@tailwind utilities;

html, body, #root {
  height: 100%;
  min-height: 100%;
}

/* Custom scrollbars for Sidebar, MessageDisplay, SettingsPage, and AccountModal */
.sidebar-scrollbar::-webkit-scrollbar,
.message-scrollbar::-webkit-scrollbar,
.settings-scrollbar::-webkit-scrollbar,
.account-scrollbar::-webkit-scrollbar {
  width: 10px;
  background: #f8fafc;
}
.sidebar-scrollbar::-webkit-scrollbar-thumb,
.message-scrollbar::-webkit-scrollbar-thumb,
.settings-scrollbar::-webkit-scrollbar-thumb,
.account-scrollbar::-webkit-scrollbar-thumb {
  background: #e2e8f0;
  border-radius: 8px;
}
.sidebar-scrollbar::-webkit-scrollbar-track,
.message-scrollbar::-webkit-scrollbar-track,
.settings-scrollbar::-webkit-scrollbar-track,
.account-scrollbar::-webkit-scrollbar-track {
  background: #f8fafc;
}

/* Dark theme scrollbars using Tailwind's .dark class */
.dark .sidebar-scrollbar::-webkit-scrollbar,
.dark .message-scrollbar::-webkit-scrollbar,
.dark .settings-scrollbar::-webkit-scrollbar,
.dark .account-scrollbar::-webkit-scrollbar {
  background: #0f172a;
}
.dark .sidebar-scrollbar::-webkit-scrollbar-thumb,
.dark .message-scrollbar::-webkit-scrollbar-thumb,
.dark .settings-scrollbar::-webkit-scrollbar-thumb,
.dark .account-scrollbar::-webkit-scrollbar-thumb {
  background: #334155;
  border-radius: 8px;
}
.dark .sidebar-scrollbar::-webkit-scrollbar-track,
.dark .message-scrollbar::-webkit-scrollbar-track,
.dark .settings-scrollbar::-webkit-scrollbar-track,
.dark .account-scrollbar::-webkit-scrollbar-track {
  background: #0f172a;
}

/* For Firefox */
.sidebar-scrollbar,
.message-scrollbar,
.settings-scrollbar,
.account-scrollbar {
  scrollbar-color: #e2e8f0 #f8fafc;
  scrollbar-width: thin;
}
.dark .sidebar-scrollbar,
.dark .message-scrollbar,
.dark .settings-scrollbar,
.dark .account-scrollbar {
  scrollbar-color: #334155 #0f172a;
}

/* Force text wrapping in message bubbles */
.message-content {
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  word-break: break-word !important;
  hyphens: auto !important;
  max-width: 100% !important;
  overflow: hidden !important;
  color: #4E4E4E;
}

/* Markdown headers, bold, and links in AI bubbles (light theme) */
.message-content h1,
.message-content h2,
.message-content h3,
.message-content h4,
.message-content h5,
.message-content h6,
.message-content strong,
.message-content b,
.message-content a {
  color: #101828;
  font-family: inherit;
  font-weight: 600;
}

/* Dark theme for AI bubbles */
.dark .message-content {
  color: #D4D4D4;
}
.dark .message-content h1,
.dark .message-content h2,
.dark .message-content h3,
.dark .message-content h4,
.dark .message-content h5,
.dark .message-content h6,
.dark .message-content strong,
.dark .message-content b,
.dark .message-content a {
  color: #FFFFFF;
  font-family: inherit;
  font-weight: 600;
}

/* Dark theme for AI bubbles */
.dark .message-content {
  color: #D4D4D4;
}
.dark .message-content h1,
.dark .message-content h2,
.dark .message-content h3,
.dark .message-content h4,
.dark .message-content h5,
.dark .message-content h6,
.dark .message-content strong,
.dark .message-content b,


.message-content * {
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  word-break: break-word !important;
  max-width: 100% !important;
}

.message-content pre {
  overflow-x: auto !important;
  white-space: pre-wrap !important;
  word-break: break-word !important;
  color: #E6E7E9 !important;
  background: #1D283A !important;
  border-radius: 0.5rem !important;
  padding: 1rem !important;
  font-family: 'JetBrains Mono', 'Fira Mono', 'Menlo', 'Consolas', monospace !important;
  font-size: 0.97em !important;
}

.message-content table {
  table-layout: fixed !important;
  width: 100% !important;
  overflow-x: auto !important;
  display: block !important;
  white-space: nowrap !important;
  background: transparent !important;
  color: inherit !important;
  font-family: inherit;
  font-size: inherit;
}

.message-content td, .message-content th {
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  word-break: break-word !important;
  white-space: normal !important;
  color: inherit !important;
  font-family: inherit;
  font-size: inherit;
}
.message-content blockquote {
  border-left: 4px solid #bdbdbd;
  margin: 1em 0;
  padding: 0.5em 1em;
  background: #f7f7fa;
  color: #4E4E4E;
  font-style: italic;
}
.dark .message-content blockquote {
  border-left: 4px solid #D4D4D4;
  background: #232b3a;
  color: #D4D4D4;
}
/* Media queries for mobile and tablet layouts */
@media (max-width: 768px) {
  .message-content {
    max-width: calc(100% - 0px) !important; /* Adjust for avatar width */
    /*padding: 0px !important; /* Increased padding for better readability */
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .message-content {
    max-width: calc(100% - 0px) !important; /* Adjust for avatar width */
    /*padding: 0px !important; /* Increased padding for better readability */
  }
}

@media (max-width: 480px) {
  .message-content {
    max-width: calc(100% - 0px) !important; /* Adjust for smaller screens */
    /*padding: 0px !important; /* Reduced padding for smaller screens */
  }
}

