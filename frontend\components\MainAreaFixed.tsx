// Simple scroll behavior fix - replace the complex useEffect with this:

useEffect(() => {
  if (!messagesContainerRef.current || messages.length === 0) return;

  const container = messagesContainerRef.current;
  const lastMessage = messages[messages.length - 1];

  // Handle new messages (when count increases)
  if (messages.length > lastMessageCountRef.current) {
    lastMessageCountRef.current = messages.length;

    if (lastMessage?.sender === 'user') {
      // User sent message - scroll to show user message at top
      setTimeout(() => {
        const userElements = container.querySelectorAll('[data-message-id]');
        const userElement = Array.from(userElements).find(
          el => el.getAttribute('data-message-id') === lastMessage.id
        ) as HTMLElement;

        if (userElement) {
          const offsetTop = userElement.offsetTop - 80;
          container.scrollTo({
            top: Math.max(0, offsetTop),
            behavior: 'smooth'
          });
        } else {
          const scrollPosition = container.scrollHeight - container.clientHeight - 150;
          container.scrollTo({
            top: Math.max(0, scrollPosition),
            behavior: 'smooth'
          });
        }
      }, 200);
    } else if (lastMessage?.sender === 'ai') {
      // AI message added - scroll to bottom
      setTimeout(() => {
        container.scrollTo({
          top: container.scrollHeight,
          behavior: 'smooth'
        });
      }, 100);
    }
  }

  // SIMPLE STREAMING AUTO-SCROLL - This is the key fix!
  if (lastMessage?.sender === 'ai' && lastMessage.isLoading) {
    // During streaming - keep scrolling to bottom
    container.scrollTop = container.scrollHeight;
  }
}, [messages]);

// Remove the second useEffect entirely - it causes conflicts