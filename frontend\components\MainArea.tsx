import React, { useState, useCallback, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import { Message, Model, User } from "../types";
import {
  MODELS,
  ChevronDownIcon,
  GithubIcon,
  TwitterXIcon,
  DiscordIcon,
  BrainIcon,
  GEMINI_MODEL_NAME,
  SidebarToggleIcon,
  PencilIcon,
  ZLogoIcon,
} from "../constants";
import ChatInput from "./ChatInput";
import MessageDisplay from "./MessageDisplay";
import UserProfileMenu from "./UserProfileMenu";
import { PASSelector } from "./PASSelector";
import { assistantService } from "../services/assistantService";
import { dataService } from "../services/dataService";
import {
  resetChatSession,
  sendMessageToGeminiStream,
} from "../services/geminiService";
import { View } from "../App";

interface MainHeaderProps {
  selectedModel: Model;
  onModelChange: (modelId: string) => void;
  isSidebarOpen: boolean;
  onToggleSidebar: () => void;
  onNewChat: () => void;
  navigateTo: (view: View) => void;
  isAuthenticated: boolean;
  currentUser: User;
  handleLogout: () => void;
  openSignInModal: () => void;
}

const ModelDropdown: React.FC<{
  selectedModel: Model;
  onModelChange: (modelId: string) => void;
}> = ({ selectedModel, onModelChange }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  return (
    <div className="relative min-w-0" ref={dropdownRef} data-oid="xl03eua">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center text-slate-800 dark:text-slate-200 hover:text-slate-900 dark:hover:text-slate-100 focus:outline-none"
        id="model-dropdown-button"
        aria-haspopup="true"
        aria-expanded={isOpen}
        data-oid="bqktk08"
      >
        <span className="text-md font-semibold truncate" data-oid="_rp:7gk">
          <span className="hidden sm:inline">{selectedModel.name}</span>
          <span className="sm:hidden">{selectedModel.name.split(' ')[0]}</span>
        </span>
        <ChevronDownIcon
          className={`w-5 h-5 ml-1 flex-shrink-0 transition-transform transform ${isOpen ? "rotate-180" : "rotate-0"}`}
          data-oid="5a.._bx"
        />
      </button>
      {isOpen && (
        <div
          className="absolute left-0 mt-2 w-56 bg-white dark:bg-slate-700 rounded-md shadow-lg ring-1 ring-black dark:ring-slate-600 ring-opacity-5 z-20 py-1"
          role="menu"
          aria-orientation="vertical"
          aria-labelledby="model-dropdown-button"
          data-oid="ws8fcjl"
        >
          {MODELS.map((model) => (
            <button
              key={model.id}
              onClick={() => {
                onModelChange(model.id);
                setIsOpen(false);
              }}
              className="block w-full text-left px-4 py-2 text-sm text-slate-700 dark:text-slate-200 hover:bg-slate-100 dark:hover:bg-slate-600"
              role="menuitem"
              data-oid="r--yrn."
            >
              {model.name}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

const MainHeader: React.FC<MainHeaderProps> = ({
  selectedModel,
  onModelChange,
  isSidebarOpen,
  onToggleSidebar,
  onNewChat,
  navigateTo,
  isAuthenticated,
  currentUser,
  handleLogout,
  openSignInModal,
}) => {
  const { t } = useTranslation();
  const [isHeaderUserMenuOpen, setIsHeaderUserMenuOpen] = useState(false);
  const headerAvatarRef = useRef<HTMLButtonElement>(null);
  const headerUserMenuButtonId = "header-user-menu-button";

  const toggleHeaderUserMenu = () => {
    setIsHeaderUserMenuOpen((prev) => !prev);
  };

  return (
    <>
      <div
        className="flex items-center justify-between px-2 sm:px-3 md:px-5 h-12 border-b border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-800 flex-shrink-0 min-w-0"
        data-oid="pimono0"
      >
        <div className="flex items-center space-x-2 sm:space-x-3 min-w-0 flex-1" data-oid="aj-ar7f">
          {(!isSidebarOpen || !isAuthenticated) && (
            <div className="flex items-center space-x-1" data-oid="yk4tv5:">
              <button
                onClick={onToggleSidebar}
                className="p-2 text-slate-500 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-200 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-md"
                aria-label={
                  isAuthenticated
                    ? isSidebarOpen
                      ? "Collapse sidebar"
                      : "Expand sidebar"
                    : t("chatHistoryAccessTitle")
                }
                data-oid="g3i7f-6"
              >
                <SidebarToggleIcon
                  className={`w-6 h-6 transition-transform duration-300 transform ${isSidebarOpen && isAuthenticated ? "rotate-0" : "rotate-180"}`}
                  data-oid="u0qs1jf"
                />
              </button>
              <button
                onClick={onNewChat}
                className="p-2 text-slate-500 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-200 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-md"
                aria-label={t("newChat")}
                data-oid="ca44qyp"
              >
                <PencilIcon className="w-5 h-5" data-oid="q_n3lbl" />
              </button>
            </div>
          )}
          <div className="min-w-0 flex-shrink" data-oid="3v8drig">
            <ModelDropdown
              selectedModel={selectedModel}
              onModelChange={onModelChange}
              data-oid="3plvaso"
            />
          </div>
        </div>
        {isAuthenticated && currentUser && currentUser.name ? (
          <button
            ref={headerAvatarRef}
            onClick={toggleHeaderUserMenu}
            className="rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-slate-800 flex-shrink-0"
            id={headerUserMenuButtonId}
            aria-haspopup="true"
            aria-expanded={isHeaderUserMenuOpen}
            aria-label="User menu"
            data-oid="9fm1l7j"
          >
            {currentUser.avatarUrl ? (
              <img
                src={currentUser.avatarUrl}
                alt={currentUser.name}
                className="w-8 h-8 sm:w-9 sm:h-9 rounded-full"
                data-oid="bkfn9em"
              />
            ) : (
              <div
                className="w-8 h-8 sm:w-9 sm:h-9 rounded-full bg-slate-300 dark:bg-slate-600 flex items-center justify-center text-slate-500 dark:text-slate-300 font-semibold text-sm"
                data-oid="wk2mxaj"
              >
                {currentUser.name.substring(0, 1).toUpperCase()}
              </div>
            )}
          </button>
        ) : !isAuthenticated ? (
          <button
            onClick={openSignInModal}
            className="px-3 sm:px-4 py-2 bg-[#222428] dark:bg-slate-700 text-white text-sm font-medium rounded-lg hover:bg-opacity-90 dark:hover:bg-slate-600 transition-colors flex-shrink-0"
            data-oid="j1av98w"
          >
            <span className="hidden sm:inline">{t("signIn")}</span>
            <span className="sm:hidden">Sign In</span>
          </button>
        ) : null}
      </div>
      {isAuthenticated && isHeaderUserMenuOpen && (
        <UserProfileMenu
          isOpen={isHeaderUserMenuOpen}
          onClose={() => setIsHeaderUserMenuOpen(false)}
          triggerRef={headerAvatarRef}
          menuPosition="bottom-right"
          triggerButtonId={headerUserMenuButtonId}
          navigateTo={navigateTo}
          handleLogout={handleLogout}
          data-oid="r9q_ux4"
        />
      )}
    </>
  );
};

const WelcomeScreen: React.FC<{
  onSendMessage: (message: string, useWebSearch: boolean) => void;
  isLoading: boolean;
  isAuthenticated: boolean;
  currentUser: User;
  selectedAssistant?: { name: string; emoji: string } | null;
}> = ({ onSendMessage, isLoading, isAuthenticated, currentUser, selectedAssistant }) => {
  const { t } = useTranslation();

  let userName = "";
  let assistantTitle = "";
  if (isAuthenticated && currentUser.name) {
    userName = currentUser.name.split("(")[0].trim();
    if (selectedAssistant) {
      assistantTitle = selectedAssistant.name;
    }
  }

  // Handler for PAS assessment selection
  const handleSelectAssessment = (assessmentId: string) => {
    // TODO: Launch assessment dialog/flow for selected assessment
    // For now, just log
    console.log('Selected assessment:', assessmentId);
  };

  return (
    <div
      className="flex-1 flex flex-col items-center justify-center p-3 text-center"
      data-oid="a_2rt6."
    >
      {/* PASSelector always visible */}
      <PASSelector onSelectAssessment={handleSelectAssessment} />
      {!isAuthenticated && (
        <ZLogoIcon
          className="w-16 h-16 text-slate-700 dark:text-slate-300 mb-6"
          data-oid="08yj91:"
        />
      )}
      <h1
        className="text-3xl sm:text-4xl font-semibold text-slate-800 dark:text-slate-100 mb-4"
        data-oid="zmae4vj"
      >
        {isAuthenticated && userName
          ? `Welcome, ${userName}!`
          : t("welcomeMessageGuest")}
      </h1>
      {isAuthenticated && assistantTitle && (
        <p className="text-lg text-slate-700 dark:text-slate-200 mb-8">
          {`I'm your ${assistantTitle} Assistant.`}
        </p>
      )}
      <ChatInput
        onSendMessage={onSendMessage}
        isLoading={isLoading}
        showSuggestions={true}
        data-oid="poki0ww"
      />
    </div>
  );
};

const Footer: React.FC<{ isAuthenticated: boolean }> = ({
  isAuthenticated,
}) => {
  const { t } = useTranslation();
  return (
    <div
      className="flex flex-col sm:flex-row items-center justify-center space-y-3 sm:space-y-0 sm:space-x-5 p-4 text-slate-500 dark:text-slate-400 bg-slate-50 dark:bg-slate-800 flex-shrink-0"
      data-oid="w.x0uw:"
    >
      {!isAuthenticated && (
        <div className="text-xs text-center sm:text-left" data-oid="zi:t8g3">
          <span data-oid="gzbc32v">{t("footerGuestText")}</span>
          <span className="hidden sm:inline mx-2" data-oid="j:396oa">
            |
          </span>
        </div>
      )}
      <div className="flex items-center space-x-5" data-oid="ypdjuvu">
        <a
          href="https://github.com/srgryzhkov/gemini-ai-chat-interface"
          target="_blank"
          rel="noopener noreferrer"
          className="hover:text-slate-700 dark:hover:text-slate-200"
          aria-label="Github"
          data-oid="i0fijke"
        >
          <GithubIcon data-oid="52o0870" />
        </a>
        <a
          href="https://ai.google.dev/gemini-api/docs/models/gemini-2.5-flash"
          target="_blank"
          rel="noopener noreferrer"
          className="hover:text-slate-700 dark:hover:text-slate-200"
          aria-label="AI Model Info"
          data-oid="6jt15ys"
        >
          <BrainIcon data-oid="oc9t4s0" />
        </a>
        <a
          href="https://x.com/srgryzhkov"
          target="_blank"
          rel="noopener noreferrer"
          className="hover:text-slate-700 dark:hover:text-slate-200"
          aria-label="Twitter / X"
          data-oid="2q2y14r"
        >
          <TwitterXIcon data-oid="srl3tpk" />
        </a>
        <a
          href="https://discordapp.com/users/358585938432327680"
          target="_blank"
          rel="noopener noreferrer"
          className="hover:text-slate-700 dark:hover:text-slate-200"
          aria-label="Discord"
          data-oid="6riat.2"
        >
          <DiscordIcon data-oid="svp2nh6" />
        </a>
      </div>
    </div>
  );
};

interface MainAreaProps {
  activeChatId: string | null;
  setActiveChatId: (id: string | null) => void;
  chatMessages: Message[];
  onUpdateChat: (
    chatId: string,
    messages: Message[],
    details?: {
      isNew?: boolean;
      title?: string;
      skipSave?: boolean;
      assistantId?: string | null;
      assistantName?: string | null;
      assistantEmoji?: string | null;
    },
  ) => void;
  isSidebarOpen: boolean;
  onToggleSidebar: () => void;
  onNewChat: () => void;
  navigateTo: (view: View) => void;
  isAuthenticated: boolean;
  currentUser: User;
  handleLogout: () => void;
  openSignInModal: () => void;
  selectedAssistantId: string | null;
}


const MainArea: React.FC<MainAreaProps> = ({
  activeChatId,
  setActiveChatId,
  chatMessages,
  onUpdateChat,
  isSidebarOpen,
  onToggleSidebar,
  onNewChat,
  navigateTo,
  isAuthenticated,
  currentUser,
  handleLogout,
  openSignInModal,
  selectedAssistantId,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [selectedModel, setSelectedModel] = useState<Model>(
    MODELS.find((m) => m.id === GEMINI_MODEL_NAME) || MODELS[0],
  );
  const [selectedAssistant, setSelectedAssistant] = useState<{ name: string; emoji: string } | null>(null);

  // Refs for scrolling behavior
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const lastMessageCountRef = useRef(0);
  // Removed unused refs - using simpler approach without intervals

  // Always use chatMessages prop as the source of truth
  const messages = chatMessages;

  // Debug: Log when messages change
  useEffect(() => {
    console.log(`📨 [MainArea] Messages updated: ${messages.length} messages`);
    if (messages.length > 0) {
      const lastMessage = messages[messages.length - 1];
      console.log(`📨 [MainArea] Last message: ${lastMessage.sender} - "${lastMessage.text?.substring(0, 50)}..." (loading: ${lastMessage.isLoading})`);
    }
  }, [messages]);

  // Debug streaming updates to see if text is changing
  useEffect(() => {
    const streamingMessage = messages.find(m => m.isLoading);
    if (streamingMessage) {
      console.log('🔄 [MainArea] Streaming message text length:', streamingMessage.text?.length, 'Text preview:', streamingMessage.text?.substring(0, 50) + '...');
    }
  }, [messages]);

  // Fetch assistant information when selectedAssistantId changes
  useEffect(() => {
    const fetchAssistant = async () => {
      if (selectedAssistantId) {
        try {
          console.log('[Assistant Debug] Fetching assistant with ID:', selectedAssistantId);
          const assistant = await assistantService.getAssistant(selectedAssistantId);
          console.log('[Assistant Debug] Assistant fetched:', assistant);
          if (assistant) {
            setSelectedAssistant({ name: assistant.name, emoji: assistant.emoji });
          } else {
            setSelectedAssistant(null);
          }
        } catch (error) {
          console.error('[Assistant Debug] Error fetching assistant:', error);
          setSelectedAssistant(null);
        }
      } else {
        setSelectedAssistant(null);
      }
    };

    fetchAssistant();
  }, [selectedAssistantId]);

  // Auto-scroll behavior - scroll to bottom when messages change
  useEffect(() => {
    if (!messagesContainerRef.current) {
      console.log('⚠️ [MainArea] messagesContainerRef is null');
      return;
    }

    const container = messagesContainerRef.current;

    // Scroll to bottom when messages change
    const scrollToBottom = () => {
      if (container) {
        const oldScrollTop = container.scrollTop;
        container.scrollTop = container.scrollHeight;
        console.log(`📜 [MainArea] Scrolled from ${oldScrollTop} to ${container.scrollTop} (height: ${container.scrollHeight})`);
      }
    };

    // Use requestAnimationFrame for better performance
    requestAnimationFrame(scrollToBottom);
  }, [messages]);

  // Additional scroll effect for streaming messages
  useEffect(() => {
    if (!messagesContainerRef.current) return;

    const container = messagesContainerRef.current;
    const lastMessage = messages[messages.length - 1];

    // If the last message is loading (streaming), keep scrolling
    if (lastMessage?.isLoading) {
      const scrollToBottom = () => {
        if (container) {
          container.scrollTop = container.scrollHeight;
        }
      };

      // Scroll immediately and then again after a short delay
      scrollToBottom();
      const timeoutId = setTimeout(scrollToBottom, 50);

      return () => clearTimeout(timeoutId);
    }
  }, [messages, messages[messages.length - 1]?.text]);




  const handleModelChange = (modelId: string) => {
    const newModel = MODELS.find((m) => m.id === modelId);
    if (newModel) {
      setSelectedModel(newModel);
      resetChatSession();
    }
  };

  const generateTitleFromMessage = (text: string): string => {
    return (
      text.split(" ").slice(0, 5).join(" ") +
      (text.split(" ").length > 5 ? "..." : "")
    );
  };

  const handleSendMessage = useCallback(
    async (inputText: string, useWebSearch: boolean) => {
      if (!currentUser) return;

      setIsLoading(true);

      const userMessageId = `user-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
      const aiMessageId = `ai-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;


      const isNewChatSession = !activeChatId;
  let currentChatIdToUse: string = activeChatId || "";

      if (isNewChatSession) {
        if (isAuthenticated) {
          // For registered users, create chat session in Supabase immediately
          const now = new Date();
          console.log('[Assistant Debug] Creating new chat session with assistant context:', {
            inputText,
            selectedAssistantId,
            selectedAssistant,
            currentUser,
            isAuthenticated
          });
          const newSession = await dataService.createChatSession(
            generateTitleFromMessage(inputText),
            "SpeechBubbleIcon",
            selectedAssistantId || null,
            selectedAssistant?.name || null,
            selectedAssistant?.emoji || null
          );
          console.log('[Assistant Debug] New chat session created:', newSession);
          if (newSession && newSession.id) {
            currentChatIdToUse = newSession.id;
            setActiveChatId(newSession.id);
          } else {
            // Fallback to temp ID if creation fails
            currentChatIdToUse = `temp-${Date.now()}`;
            setActiveChatId(currentChatIdToUse);
          }
        } else {
          // For guests, use a temporary ID
          console.log('[Assistant Debug] Creating temp chat session for guest:', {
            inputText,
            selectedAssistantId,
            selectedAssistant,
            currentUser,
            isAuthenticated
          });
          currentChatIdToUse = `temp-${Date.now()}`;
          setActiveChatId(currentChatIdToUse);
        }

      // Ensure currentChatIdToUse is always a string
      if (!currentChatIdToUse) {
        currentChatIdToUse = `temp-${Date.now()}`;
        setActiveChatId(currentChatIdToUse);
      }
      }

      const newUserMessage: Message = {
        id: userMessageId,
        text: inputText,
        sender: "user",
        timestamp: new Date(),
        avatar: currentUser.avatarUrl,
        chatId: currentChatIdToUse,
      };

      const updatedMessagesAfterUser = [...messages, newUserMessage];

      // Update chat with user message first and trigger scroll to user message
      const aiPlaceholderMessage: Message = {
        id: aiMessageId,
        text: "",
        sender: "ai",
        timestamp: new Date(),
        isLoading: true,
        chatId: currentChatIdToUse,
      };
      const updatedMessagesWithPlaceholder = [
        ...updatedMessagesAfterUser,
        aiPlaceholderMessage,
      ];

      // Simple approach: Create chat with user message and AI placeholder
      console.log('🚀 [handleSendMessage] Creating/updating chat with user message and AI placeholder');
      console.log('[Assistant Debug] onUpdateChat called with:', {
        currentChatIdToUse,
        updatedMessagesWithPlaceholder,
        assistantContext: {
          selectedAssistantId,
          selectedAssistant
        }
      });
      onUpdateChat(currentChatIdToUse, updatedMessagesWithPlaceholder, {
        isNew: isNewChatSession,
        title: isNewChatSession ? generateTitleFromMessage(inputText) : undefined,
        assistantId: selectedAssistantId,
        assistantName: selectedAssistant?.name || null,
        assistantEmoji: selectedAssistant?.emoji || null,
      });

      let accumulatedAiText = "";
      let finalGroundingChunks: any[] | undefined;
      let finalMessagesForApp: Message[] = updatedMessagesWithPlaceholder;
      let chunkCount = 0;

      try {
        // Get the selected assistant for context
        let assistant = null;
        if (selectedAssistantId && assistantService) {
          try {
            console.log('🔍 [handleSendMessage] Fetching assistant:', selectedAssistantId);
            console.log('[Assistant Debug] Fetching assistant for message context:', {
              selectedAssistantId,
              selectedAssistant
            });
            assistant = await assistantService.getAssistant(selectedAssistantId);
            console.log('[Assistant Debug] Assistant used for message:', assistant);
            console.log('🤖 [handleSendMessage] Retrieved assistant:', assistant?.name || 'null');
          } catch (error) {
            console.error('❌ [handleSendMessage] Error fetching assistant:', error);
            console.error('[Assistant Debug] Error fetching assistant for message:', error);
            assistant = null;
          }
        } else {
          console.log('⚠️ [handleSendMessage] No assistant selected or service unavailable');
          console.log('[Assistant Debug] No assistant selected or service unavailable:', {
            selectedAssistantId,
            selectedAssistant
          });
        }

        // Use direct Gemini service for now (simpler and more reliable)
        const systemInstruction = assistant?.instructions || 'You are a helpful and friendly AI assistant.';
        const parameters = assistant?.parameters || {};
        console.log('[Assistant Debug] System instruction and parameters for Gemini:', {
          systemInstruction,
          parameters
        });
        console.log('🤖 [handleSendMessage] Using system instruction:', systemInstruction.substring(0, 100) + '...');
        console.log('🤖 [handleSendMessage] Using parameters:', parameters);

        const stream = sendMessageToGeminiStream(inputText, useWebSearch, systemInstruction, parameters);

        for await (const chunk of stream) {
          if (chunk.error) {
            accumulatedAiText = `Error: ${chunk.error}`;
            finalMessagesForApp = updatedMessagesWithPlaceholder.map((msg) =>
              msg.id === aiMessageId
                ? {
                  ...msg,
                  text: accumulatedAiText,
                  isLoading: false,
                  isError: true,
                }
                : msg,
            );
            // Error will be handled in final save below
            break;
          }
          if (chunk.textChunk) {
            accumulatedAiText += chunk.textChunk;
            chunkCount++;
          }
          if (chunk.finalGroundingChunks) {
            finalGroundingChunks = chunk.finalGroundingChunks;
          }
          finalMessagesForApp = updatedMessagesWithPlaceholder.map((msg) =>
            msg.id === aiMessageId
              ? {
                ...msg,
                text: accumulatedAiText,
                isLoading: true,
                groundingChunks: finalGroundingChunks || msg.groundingChunks,
              }
              : msg,
          );

          // Debug: Log the AI message being updated
          if (chunkCount % 10 === 0) {
            const aiMsg = finalMessagesForApp.find(m => m.id === aiMessageId);
            console.log(`📝 [handleSendMessage] AI message text preview: "${aiMsg?.text?.substring(0, 50)}..."`);
          }
          // Update UI more frequently for better streaming experience
          if (chunkCount <= 5 || chunkCount % 2 === 0 || chunk.finalGroundingChunks) {
            console.log(`🔄 [handleSendMessage] UI update #${chunkCount}, text length: ${accumulatedAiText.length}`);
            console.log(`🔄 [handleSendMessage] Updating with messages:`, finalMessagesForApp.map(m => `${m.sender}: ${m.text?.substring(0, 30)}...`));
            onUpdateChat(currentChatIdToUse, finalMessagesForApp, {
              skipSave: true, // Only update UI, don't save to database during streaming
            });
          }
        }
        
        console.log('🔍 [handleSendMessage] Stream completed, preparing final AI message for saving');
      } catch (error) {
        console.error("Error handling stream:", error);
        const errorText = error instanceof Error ? error.message : "Unknown error occurred";
        accumulatedAiText = `Error: ${errorText}`;
        finalMessagesForApp = updatedMessagesWithPlaceholder.map((msg) =>
          msg.id === aiMessageId
            ? {
              ...msg,
              text: accumulatedAiText,
              isLoading: false,
              isError: true,
            }
            : msg,
        );
        // Error will be handled in final save below
      }

      // Final update: Mark AI response as complete and save
      console.log('✅ [handleSendMessage] Finalizing AI response');
      const finalMessages = finalMessagesForApp.map((msg) =>
        msg.id === aiMessageId
          ? {
            ...msg,
            text: accumulatedAiText,
            isLoading: false,
            groundingChunks: finalGroundingChunks || msg.groundingChunks,
          }
          : msg,
      );

      // Save final state to database
      console.log('💾 [handleSendMessage] Saving final messages to database');
      onUpdateChat(currentChatIdToUse, finalMessages, {
        skipSave: false, // Save to database
      });

      setIsLoading(false);
    },
    [activeChatId, setActiveChatId, currentUser, messages, onUpdateChat],
  );

  return (
    <div
      className="flex-1 flex flex-col bg-slate-50 dark:bg-slate-900 min-h-0"
      data-oid="0mie:v2"
    >
      <MainHeader
        selectedModel={selectedModel}
        onModelChange={handleModelChange}
        isSidebarOpen={isSidebarOpen}
        onToggleSidebar={onToggleSidebar}
        onNewChat={onNewChat}
        navigateTo={navigateTo}
        isAuthenticated={isAuthenticated}
        currentUser={currentUser}
        handleLogout={handleLogout}
        openSignInModal={openSignInModal}
        data-oid="xys1yk3"
      />

      {messages.length === 0 && !activeChatId ? (
        <div className="flex-1 flex flex-col min-h-0" data-oid="6pm5mmr">
          <WelcomeScreen
            onSendMessage={handleSendMessage}
            isLoading={isLoading}
            isAuthenticated={isAuthenticated}
            currentUser={currentUser}
            selectedAssistant={selectedAssistant}
            data-oid="75vb0hf"
          />

          <Footer isAuthenticated={isAuthenticated} data-oid="-r92twn" />
        </div>
      ) : (
        <div
          className="flex-1 flex flex-col min-h-0 overflow-hidden"
          data-oid="ce8-4c."
        >
          {/* Assistant Info Bar */}

          <div
            ref={messagesContainerRef}
            className="flex-1 overflow-y-auto message-scrollbar"
            data-oid="q2b_r9s"
          >
            <MessageDisplay
              messages={messages}
              data-oid="q68372v"
            />
          </div>
          <div
            className="p-3 bg-slate-50 dark:bg-slate-900 border-t border-slate-200 dark:border-slate-700/50 flex-shrink-0"
            data-oid="3p5mcap"
          >
            <ChatInput
              onSendMessage={handleSendMessage}
              isLoading={isLoading}
              data-oid="m32kuux"
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default MainArea;
