

import { GoogleGenAI, GenerateContentResponse, Chat, SendMessageParameters, Candidate, GroundingChunk as GeminiGroundingChunk } from "@google/genai";
import { GEMINI_MODEL_NAME } from '../constants';
import { GroundingChunk } from "../types"; // This is the app's internal type

// Access Gemini API key from Vite environment variables
const GEMINI_API_KEY = import.meta.env.VITE_GEMINI_API_KEY;

const effectiveApiKey = GEMINI_API_KEY || "DUMMY_KEY_FOR_UI_DEV";

if (!GEMINI_API_KEY && effectiveApiKey === "DUMMY_KEY_FOR_UI_DEV") {
  // Remove all console.log and console.error statements
} else if (!GEMINI_API_KEY && effectiveApiKey !== "DUMMY_KEY_FOR_UI_DEV") {
  // This case implies effectiveApiKey was set by some other means if DUMMY_KEY logic changes
  // Remove all console.log and console.error statements
}


const ai = new GoogleGenAI({ apiKey: effectiveApiKey });
let chatInstance: Chat | null = null;
let currentSystemInstruction: string = 'You are a helpful and friendly AI assistant.';
let currentParameters: Record<string, any> = {};

const initializeChat = (systemInstruction?: string, parameters?: Record<string, any>): Chat => {
  if (effectiveApiKey === "DUMMY_KEY_FOR_UI_DEV") {
     // Remove all console.log and console.error statements
  }

  const instruction = systemInstruction || currentSystemInstruction;
  const params = parameters || currentParameters;

  return ai.chats.create({
    model: GEMINI_MODEL_NAME,
    config: {
      systemInstruction: instruction,
      ...params, // Spread assistant parameters (temperature, top_p, etc.)
    },
  });
};

const getChatInstance = (systemInstruction?: string, parameters?: Record<string, any>): Chat => {
  // If system instruction or parameters changed, reset the chat instance
  if (systemInstruction && systemInstruction !== currentSystemInstruction) {
    currentSystemInstruction = systemInstruction;
    chatInstance = null;
  }

  if (parameters && JSON.stringify(parameters) !== JSON.stringify(currentParameters)) {
    currentParameters = parameters;
    chatInstance = null;
  }

  if (!chatInstance) {
    chatInstance = initializeChat(systemInstruction, parameters);
  }
  return chatInstance;
};

export const resetChatSession = () => {
  chatInstance = null;
}

export const updateAssistantContext = (systemInstruction: string, parameters?: Record<string, any>) => {
  currentSystemInstruction = systemInstruction;
  currentParameters = parameters || {};
  chatInstance = null; // Force recreation with new context
}

const extractGroundingChunks = (candidate: Candidate | undefined): GroundingChunk[] => {
  if (!candidate || !candidate.groundingMetadata || !candidate.groundingMetadata.groundingChunks) {
    return [];
  }
  return candidate.groundingMetadata.groundingChunks.map((chunk: GeminiGroundingChunk): GroundingChunk => ({ // Map SDK type to app's internal type
    web: chunk.web ? { uri: chunk.web.uri || '', title: chunk.web.title || '' } : undefined,
    retrievedContext: chunk.retrievedContext ? { uri: chunk.retrievedContext.uri || '', title: chunk.retrievedContext.title || '' } : undefined,
  }));
};


export const sendMessageToGemini = async (
  messageText: string,
  useWebSearch: boolean,
  systemInstruction?: string,
  parameters?: Record<string, any>
): Promise<{ text: string; groundingChunks?: GroundingChunk[] }> => {
  if (effectiveApiKey === "DUMMY_KEY_FOR_UI_DEV") {
    await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate network delay
    return { text: `Mock response (dummy API key). You said: "${messageText}". Web search: ${useWebSearch ? 'Enabled' : 'Disabled'}` };
  }

  try {
    const chat = getChatInstance(systemInstruction, parameters);
    
    const request: SendMessageParameters = {
        message: messageText,
    };

    if (useWebSearch) {
        request.config = {
            ...(request.config || {}), 
            tools: [{ googleSearch: {} }],
        };
    }
    
    const response: GenerateContentResponse = await chat.sendMessage(request);
    const responseText = response.text;
    const groundingChunks = extractGroundingChunks(response.candidates?.[0]);

    return { text: responseText, groundingChunks };

  } catch (error) {
    if (error instanceof Error) {
        return { text: `Error: ${error.message}` };
    }
    return { text: "An unknown error occurred while contacting the AI." };
  }
};


export async function* sendMessageToGeminiStream(
  messageText: string,
  useWebSearch: boolean,
  systemInstruction?: string,
  parameters?: Record<string, any>
): AsyncGenerator<{ textChunk?: string; finalGroundingChunks?: GroundingChunk[]; error?: string }> {
  if (effectiveApiKey === "DUMMY_KEY_FOR_UI_DEV") {
    const mockMessage = `Mock stream response (dummy API key). You said: "${messageText}". Web search: ${useWebSearch ? 'Enabled' : 'Disabled'}`;
    // Simulate streaming
    for (let i = 0; i < mockMessage.length; i += 10) {
        yield { textChunk: mockMessage.substring(i, Math.min(i + 10, mockMessage.length)) };
        await new Promise(resolve => setTimeout(resolve, 50));
    }
    yield { finalGroundingChunks: [] };
    return;
  }

  try {
    const chat = getChatInstance(systemInstruction, parameters);

    const request: SendMessageParameters = {
        message: messageText,
    };

     if (useWebSearch) {
        request.config = {
            ...(request.config || {}),
            tools: [{ googleSearch: {} }],
        };
    }

    const stream = await chat.sendMessageStream(request);
    let finalGroundingChunks: GroundingChunk[] | undefined;

    for await (const chunk of stream) {
      finalGroundingChunks = extractGroundingChunks(chunk.candidates?.[0]);
      yield { textChunk: chunk.text, finalGroundingChunks }; // Yield grounding chunks with each text chunk if available
    }
     // It's possible that groundingMetadata comes only in the last chunk or after all text.
     // The current structure yields it on each chunk if present. If it's only at the end,
     // this will correctly pass the last known version.

  } catch (error) {
    if (error instanceof Error) {
        yield { error: error.message };
    } else {
        yield { error: "An unknown error occurred during streaming." };
    }
  }
}