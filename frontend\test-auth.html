<!DOCTYPE html>
<html>
<head>
    <title>Supabase Auth Test</title>
</head>
<body>
    <h1>Supabase Authentication Test</h1>
    
    <div id="auth-section">
        <h2>Sign In</h2>
        <input type="email" id="email" placeholder="Email" value="<EMAIL>">
        <input type="password" id="password" placeholder="Password" value="test123456">
        <button onclick="signIn()">Sign In</button>
        <button onclick="signOut()">Sign Out</button>
    </div>

    <div id="test-section" style="margin-top: 20px;">
        <h2>Message Testing</h2>
        <input type="text" id="test-message" placeholder="Test message" value="This is a test message">
        <button onclick="testAddMessage()">Test Add Message</button>
        <button onclick="testCreateChatAndMessage()">Test Create Chat + Message</button>
        <button onclick="testDataServiceFlow()">Test DataService Flow</button>
    </div>

    <div id="status"></div>
    <div id="data"></div>

    <script type="module">
        import { supabase, supabaseHelpers } from './lib/supabase.js';
        
        window.supabase = supabase;
        window.supabaseHelpers = supabaseHelpers;
        
        // Check initial auth state
        supabase.auth.getSession().then(({ data: { session } }) => {
            updateUI(session);
        });
        
        // Listen for auth changes
        supabase.auth.onAuthStateChange((event, session) => {
            console.log('Auth event:', event, session);
            updateUI(session);
        });
        
        window.signIn = async () => {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            console.log('Attempting to sign in with:', email);
            
            const { data, error } = await supabase.auth.signInWithPassword({
                email,
                password
            });
            
            if (error) {
                console.error('Sign in error:', error);
                document.getElementById('status').innerHTML = `❌ Error: ${error.message}`;
            } else {
                console.log('Sign in successful:', data);
                document.getElementById('status').innerHTML = '✅ Signed in successfully!';
                loadUserData();
            }
        };
        
        window.signOut = async () => {
            const { error } = await supabase.auth.signOut();
            if (error) {
                console.error('Sign out error:', error);
            } else {
                console.log('Signed out successfully');
                document.getElementById('status').innerHTML = '👋 Signed out';
                document.getElementById('data').innerHTML = '';
            }
        };

        window.testAddMessage = async () => {
            const messageText = document.getElementById('test-message').value;
            if (!messageText.trim()) {
                alert('Please enter a test message');
                return;
            }

            try {
                // Get current user
                const { data: { user } } = await supabase.auth.getUser();
                if (!user) {
                    alert('Please sign in first');
                    return;
                }

                // Get first chat session for this user
                const { data: chatSessions, error: chatError } = await supabase
                    .from('chat_sessions')
                    .select('*')
                    .eq('user_id', user.id)
                    .limit(1);

                if (chatError) {
                    console.error('Error getting chat sessions:', chatError);
                    alert('Error getting chat sessions: ' + chatError.message);
                    return;
                }

                if (!chatSessions || chatSessions.length === 0) {
                    alert('No chat sessions found. Please create one first.');
                    return;
                }

                const chatSession = chatSessions[0];

                // Add message using supabaseHelpers
                const { data: newMessage, error: messageError } = await supabaseHelpers.addMessage({
                    chat_session_id: chatSession.id,
                    content: messageText,
                    sender_type: 'user',
                    metadata: { test: true },
                    is_loading: false,
                    is_error: false,
                    avatar_url: null,
                    grounding_chunks: []
                });

                if (messageError) {
                    console.error('Error adding message:', messageError);
                    alert('Error adding message: ' + messageError.message);
                } else {
                    console.log('Message added successfully:', newMessage);
                    alert('Message added successfully! ID: ' + newMessage.id);
                    loadUserData(); // Refresh data
                }

            } catch (error) {
                console.error('Error in testAddMessage:', error);
                alert('Error: ' + error.message);
            }
        };

        window.testCreateChatAndMessage = async () => {
            const messageText = document.getElementById('test-message').value;
            if (!messageText.trim()) {
                alert('Please enter a test message');
                return;
            }

            try {
                // Get current user
                const { data: { user } } = await supabase.auth.getUser();
                if (!user) {
                    alert('Please sign in first');
                    return;
                }

                // Create new chat session
                const { data: newChat, error: chatError } = await supabaseHelpers.createChatSession(
                    user.id,
                    'Test Chat - ' + new Date().toLocaleTimeString(),
                    'SpeechBubbleIcon'
                );

                if (chatError) {
                    console.error('Error creating chat session:', chatError);
                    alert('Error creating chat session: ' + chatError.message);
                    return;
                }

                console.log('Chat session created:', newChat);

                // Add message to the new chat
                const { data: newMessage, error: messageError } = await supabaseHelpers.addMessage({
                    chat_session_id: newChat.id,
                    content: messageText,
                    sender_type: 'user',
                    metadata: { test: true },
                    is_loading: false,
                    is_error: false,
                    avatar_url: null,
                    grounding_chunks: []
                });

                if (messageError) {
                    console.error('Error adding message:', messageError);
                    alert('Error adding message: ' + messageError.message);
                } else {
                    console.log('Message added successfully:', newMessage);
                    alert('Chat and message created successfully! Chat ID: ' + newChat.id + ', Message ID: ' + newMessage.id);
                    loadUserData(); // Refresh data
                }

            } catch (error) {
                console.error('Error in testCreateChatAndMessage:', error);
                alert('Error: ' + error.message);
            }
        };

        window.testDataServiceFlow = async () => {
            const messageText = document.getElementById('test-message').value;
            if (!messageText.trim()) {
                alert('Please enter a test message');
                return;
            }

            try {
                // Import dataService from the main app
                const { dataService } = await import('./services/dataService.js');

                // Check authentication
                const isAuth = await dataService.isAuthenticated();
                console.log('Authentication status:', isAuth);

                if (!isAuth) {
                    alert('Please sign in first');
                    return;
                }

                // Get current user
                const currentUser = await dataService.getCurrentUser();
                console.log('Current user:', currentUser);

                // Create a test chat session
                const newChat = await dataService.createChatSession(
                    'Test Chat via DataService - ' + new Date().toLocaleTimeString(),
                    'SpeechBubbleIcon'
                );

                if (!newChat) {
                    alert('Failed to create chat session');
                    return;
                }

                console.log('Created chat session:', newChat);

                // Create a test message
                const testMessage = {
                    text: messageText,
                    sender: 'user',
                    chatId: newChat.id,
                    isLoading: false,
                    isError: false,
                    avatar: currentUser?.avatarUrl || null,
                    groundingChunks: []
                };

                // Add message using dataService
                const savedMessage = await dataService.addMessage(newChat.id, testMessage);

                if (savedMessage) {
                    console.log('Message saved successfully:', savedMessage);
                    alert('Success! Chat ID: ' + newChat.id + ', Message ID: ' + savedMessage.id);
                    loadUserData(); // Refresh data
                } else {
                    alert('Failed to save message');
                }

            } catch (error) {
                console.error('Error in testDataServiceFlow:', error);
                alert('Error: ' + error.message);
            }
        };
        
        function updateUI(session) {
            if (session) {
                document.getElementById('status').innerHTML = `✅ Authenticated as: ${session.user.email}`;
                loadUserData();
            } else {
                document.getElementById('status').innerHTML = '❌ Not authenticated';
                document.getElementById('data').innerHTML = '';
            }
        }
        
        async function loadUserData() {
            try {
                // Get user profile
                const { data: profile, error: profileError } = await supabase
                    .from('profiles')
                    .select('*')
                    .single();
                
                // Get chat sessions
                const { data: chats, error: chatsError } = await supabase
                    .from('chat_sessions')
                    .select('*');
                
                // Get messages
                const { data: messages, error: messagesError } = await supabase
                    .from('messages')
                    .select('*');
                
                let html = '<h2>User Data</h2>';
                
                if (profileError) {
                    html += `<p>❌ Profile error: ${profileError.message}</p>`;
                } else {
                    html += `<p>✅ Profile: ${profile?.name} (${profile?.email})</p>`;
                }
                
                if (chatsError) {
                    html += `<p>❌ Chats error: ${chatsError.message}</p>`;
                } else {
                    html += `<p>✅ Chat sessions: ${chats?.length || 0}</p>`;
                    if (chats && chats.length > 0) {
                        html += '<ul>';
                        chats.forEach(chat => {
                            html += `<li>${chat.title} (${chat.created_at})</li>`;
                        });
                        html += '</ul>';
                    }
                }
                
                if (messagesError) {
                    html += `<p>❌ Messages error: ${messagesError.message}</p>`;
                } else {
                    html += `<p>✅ Messages: ${messages?.length || 0}</p>`;
                }
                
                document.getElementById('data').innerHTML = html;
                
            } catch (error) {
                console.error('Error loading user data:', error);
                document.getElementById('data').innerHTML = `❌ Error: ${error.message}`;
            }
        }
    </script>
</body>
</html>
