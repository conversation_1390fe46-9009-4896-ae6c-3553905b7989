// --- Debug: Send console output to backend for real-time logging ---
const origLog = console.log;
const origError = console.error;
function sendLogToServer(type: string, ...args: any[]) {
  fetch('http://localhost:3001/api/log', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ type, message: args.map(String).join(' '), timestamp: new Date().toISOString() })
  });
}
console.log = function(...args: any[]) {
  origLog.apply(console, args);
  sendLogToServer('log', ...args);
};
console.error = function(...args: any[]) {
  origError.apply(console, args);
  sendLogToServer('error', ...args);
};
// Persist Supabase recovery token from hash to sessionStorage before React renders
(function persistSupabaseRecoveryToken() {
  if (typeof window !== "undefined" && window.location.hash) {
    const hash = window.location.hash.substring(1); // remove '#'
    const params = new URLSearchParams(hash);
    const accessToken = params.get("access_token");
    const refreshToken = params.get("refresh_token");
    const type = params.get("type");
    if (accessToken && type === "recovery") {
      sessionStorage.setItem("supabase_recovery_access_token", accessToken);
      sessionStorage.setItem("supabase_recovery_type", type);
      if (refreshToken) {
        sessionStorage.setItem("supabase_recovery_refresh_token", refreshToken);
      }
    }
  }
})();

import React from "react";
import ReactDOM from "react-dom/client";
import App from "./App";
import { I18nextProvider } from "react-i18next";
import i18n, { initializeI18n } from "./i18n"; // Import the i18n instance and the new initializer
import { BrowserRouter } from "react-router-dom";

const rootElement = document.getElementById("root");
if (!rootElement) {
  throw new Error("Could not find root element to mount to");
}

const root = ReactDOM.createRoot(rootElement);

// Initialize i18next and then render the app
initializeI18n()
  .then((initializedI18nInstance) => {
    root.render(
      <React.StrictMode>
        <I18nextProvider i18n={initializedI18nInstance}>
          <BrowserRouter>
            <App />
          </BrowserRouter>
        </I18nextProvider>
      </React.StrictMode>,
    );
  })
  .catch((error) => {
    // Optionally render a user-friendly error message to the DOM
    root.render(
      <div
        style={{
          padding: "20px",
          textAlign: "center",
          fontFamily: "sans-serif",
          color: "#333",
        }}
      >
        <h1>Application Error</h1>
        <p>
          There was an issue loading essential application resources
          (translations).
        </p>
        <p>
          Please try refreshing the page. If the problem persists, contact
          support.
        </p>
      </div>,
    );
  });
