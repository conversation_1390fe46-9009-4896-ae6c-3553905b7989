#!/usr/bin/env node

/**
 * Data Migration Script for AI Chat Full Stack
 * Migrates data from JSON files to Supabase database
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_KEY;

// Validate environment variables
if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
    console.error('❌ Error: Missing environment variables');
    console.error('Please set SUPABASE_URL and SUPABASE_SERVICE_KEY');
    console.error('Example:');
    console.error('export SUPABASE_URL="https://your-project.supabase.co"');
    console.error('export SUPABASE_SERVICE_KEY="your-service-role-key"');
    process.exit(1);
}

// Initialize Supabase client with service role key (bypasses RLS)
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY, {
    auth: {
        autoRefreshToken: false,
        persistSession: false
    }
});

// Helper function to read JSON files
function readJsonFile(filePath) {
    try {
        const fullPath = path.join(__dirname, filePath);
        const data = fs.readFileSync(fullPath, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error(`❌ Error reading ${filePath}:`, error.message);
        return null;
    }
}

// Helper function to generate UUID v4
function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

async function createTestUser() {
    console.log('👤 Creating test user...');
    
    const testUserId = generateUUID();
    const testEmail = '<EMAIL>';
    
    // Create user in auth.users table directly (simulating signup)
    const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
        email: testEmail,
        password: 'test123456', // Temporary password
        email_confirm: true,
        user_metadata: {
            name: 'Sergey Ryzhkov (Сергей Рыжков)',
            avatar_url: 'https://picsum.photos/seed/user1/40/40'
        }
    });

    if (authError) {
        console.error('❌ Error creating auth user:', authError.message);
        return null;
    }

    console.log('✅ Test user created successfully');
    return authUser.user;
}

async function migrateUserData(userId) {
    console.log('📝 Migrating user profile data...');
    
    const usersData = readJsonFile('frontend/data/users.json');
    if (!usersData) return false;

    const loggedInUser = usersData.loggedInUser;
    
    // Insert/update profile data
    const { error: profileError } = await supabase
        .from('profiles')
        .upsert({
            id: userId,
            name: loggedInUser.name,
            email: loggedInUser.email,
            avatar_url: loggedInUser.avatarUrl
        });

    if (profileError) {
        console.error('❌ Error migrating profile:', profileError.message);
        return false;
    }

    // Insert default user preferences
    const { error: prefsError } = await supabase
        .from('user_preferences')
        .upsert({
            user_id: userId,
            theme: 'system',
            language: 'en',
            settings: {}
        });

    if (prefsError) {
        console.error('❌ Error migrating preferences:', prefsError.message);
        return false;
    }

    console.log('✅ User profile data migrated successfully');
    return true;
}

async function migrateChatData(userId) {
    console.log('💬 Migrating chat sessions and messages...');
    
    const chatsData = readJsonFile('frontend/data/chats.json');
    if (!chatsData) return false;

    let migratedChats = 0;
    let migratedMessages = 0;

    for (const chat of chatsData) {
        try {
            // Insert chat session
            const { data: chatSession, error: chatError } = await supabase
                .from('chat_sessions')
                .insert({
                    id: chat.id,
                    user_id: userId,
                    title: chat.title,
                    icon_name: chat.iconName || 'SpeechBubbleIcon',
                    created_at: new Date(chat.lastActivity).toISOString(),
                    updated_at: new Date(chat.lastActivity).toISOString()
                })
                .select()
                .single();

            if (chatError) {
                console.error(`❌ Error migrating chat ${chat.id}:`, chatError.message);
                continue;
            }

            migratedChats++;
            console.log(`  ✅ Migrated chat: "${chat.title}"`);

            // Insert messages for this chat
            if (chat.messages && chat.messages.length > 0) {
                const messages = chat.messages.map(msg => ({
                    id: msg.id,
                    chat_session_id: chat.id,
                    content: msg.text,
                    sender_type: msg.sender,
                    timestamp: new Date(msg.timestamp).toISOString(),
                    metadata: {
                        chatId: msg.chatId,
                        originalId: msg.id
                    },
                    is_loading: msg.isLoading || false,
                    is_error: msg.isError || false,
                    avatar_url: msg.avatar || null,
                    grounding_chunks: msg.groundingChunks || []
                }));

                const { error: messagesError } = await supabase
                    .from('messages')
                    .insert(messages);

                if (messagesError) {
                    console.error(`❌ Error migrating messages for chat ${chat.id}:`, messagesError.message);
                } else {
                    migratedMessages += messages.length;
                    console.log(`    ✅ Migrated ${messages.length} messages`);
                }
            }

        } catch (error) {
            console.error(`❌ Unexpected error migrating chat ${chat.id}:`, error.message);
        }
    }

    console.log(`✅ Migration complete: ${migratedChats} chats, ${migratedMessages} messages`);
    return true;
}

async function verifyMigration(userId) {
    console.log('🔍 Verifying migration...');
    
    // Check profile
    const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

    if (profileError || !profile) {
        console.error('❌ Profile verification failed');
        return false;
    }

    // Check chat sessions
    const { data: chatSessions, error: chatsError } = await supabase
        .from('chat_sessions')
        .select('*')
        .eq('user_id', userId);

    if (chatsError) {
        console.error('❌ Chat sessions verification failed');
        return false;
    }

    // Check messages
    const { data: messages, error: messagesError } = await supabase
        .from('messages')
        .select('*')
        .in('chat_session_id', chatSessions.map(c => c.id));

    if (messagesError) {
        console.error('❌ Messages verification failed');
        return false;
    }

    console.log('✅ Verification successful:');
    console.log(`  - Profile: ${profile.name} (${profile.email})`);
    console.log(`  - Chat sessions: ${chatSessions.length}`);
    console.log(`  - Messages: ${messages.length}`);
    
    return true;
}

async function main() {
    console.log('🚀 Starting data migration to Supabase...\n');

    try {
        // Step 1: Create test user
        const user = await createTestUser();
        if (!user) {
            console.error('❌ Failed to create test user. Aborting migration.');
            process.exit(1);
        }

        // Step 2: Migrate user data
        const userMigrated = await migrateUserData(user.id);
        if (!userMigrated) {
            console.error('❌ Failed to migrate user data. Aborting migration.');
            process.exit(1);
        }

        // Step 3: Migrate chat data
        const chatsMigrated = await migrateChatData(user.id);
        if (!chatsMigrated) {
            console.error('❌ Failed to migrate chat data. Aborting migration.');
            process.exit(1);
        }

        // Step 4: Verify migration
        const verified = await verifyMigration(user.id);
        if (!verified) {
            console.error('❌ Migration verification failed.');
            process.exit(1);
        }

        console.log('\n🎉 Data migration completed successfully!');
        console.log('\n📋 Next steps:');
        console.log('1. Install Supabase client in your frontend');
        console.log('2. Configure environment variables');
        console.log('3. Update frontend to use Supabase instead of JSON files');
        console.log('\nTest user credentials:');
        console.log(`Email: <EMAIL>`);
        console.log(`Password: test123456`);

    } catch (error) {
        console.error('❌ Migration failed:', error.message);
        process.exit(1);
    }
}

// Run the migration
if (require.main === module) {
    main();
}

module.exports = { main };
