import React from 'react';

interface PASCardProps {
  id: string;
  title: string;
  description: string;
  icon?: string;
  onSelect: (id: string) => void;
}

export const PASCard: React.FC<PASCardProps> = ({ id, title, description, icon, onSelect }) => (
  <button
    className="flex flex-col items-center justify-center bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-md p-4 m-2 w-40 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-primary-500 transition"
    onClick={() => onSelect(id)}
    aria-label={title}
    type="button"
  >
    {icon && <img src={icon} alt="" className="w-10 h-10 mb-2" />}
    <span className="font-semibold text-lg mb-1">{title}</span>
    <span className="text-sm text-gray-500 dark:text-gray-400 text-center">{description}</span>
  </button>
);
