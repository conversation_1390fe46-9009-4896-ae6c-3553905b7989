// types/assistant.ts

export interface Assistant {
  id: string;
  emoji: string;
  name: string;
  description: string;
  instructions: string;
  parameters?: Record<string, any>;
}

// Extended interface for database operations (includes user_id, system flag, access level, and timestamps)
export interface DatabaseAssistant extends Assistant {
  user_id: string | null;
  is_system: boolean;
  access_level: 'free' | 'premium' | 'enterprise';
  created_at: string;
  updated_at: string;
}
