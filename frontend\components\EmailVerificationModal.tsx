import React from "react";
import { useTranslation } from "react-i18next";
import { CloseIcon } from "../constants";

interface EmailVerificationModalProps {
  isOpen: boolean;
  onClose: () => void;
  email: string;
}

const EmailVerificationModal: React.FC<EmailVerificationModalProps> = ({
  isOpen,
  onClose,
  email,
}) => {
  const { t } = useTranslation();
  
  if (!isOpen) return null;
  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/40"
      onClick={e => { if (e.target === e.currentTarget) onClose(); }}
      role="dialog"
      aria-modal="true"
    >
      <div className="w-full sm:max-w-md min-h-screen flex flex-col relative">
        <div className="my-auto w-full">
          <div className="dark:text-gray-100 rounded-2xl bg-white dark:bg-slate-800 border-b-2 border-black/10 p-10 flex flex-col items-center justify-center relative">
            <button
              onClick={onClose}
              className="absolute top-4 right-4 p-1.5 text-slate-400 dark:text-slate-500 hover:text-slate-600 dark:hover:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-full"
              aria-label="Close"
            >
              <CloseIcon className="w-5 h-5" />
            </button>
            <div className="mb-6 size-14 self-center">
              <div className="h-full w-full aspect-square rounded-2xl bg-gradient-to-br from-[#1A1919] to-[#64646B] flex justify-center items-center">
                {/* No logo, just reserved space */}
              </div>
            </div>
            <div className="mb-4">
              <div className="text-2xl text-center font-bold textGradient bg-gradient-to-r from-[#191a1d] via-[#888888] to-[#191a1d] bg-clip-text text-transparent mb-2">{t('verifyYourEmail')}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400 font-normal mb-4 mt-2 text-center">
                {t('emailVerificationMessage', { email: email })}
              </div>
            </div>
            <button
              className="ButtonSkipForNow w-full flex items-center justify-center bg-slate-100 dark:bg-slate-700 hover:bg-slate-200 dark:hover:bg-slate-600 text-slate-700 dark:text-slate-200 font-medium py-3 px-4 rounded-lg mb-1 transition-colors"
              onClick={onClose}
            >
              {t('close')}
            </button>
          </div>
          
        </div>
      </div>
    </div>
  );
};

export default EmailVerificationModal;
