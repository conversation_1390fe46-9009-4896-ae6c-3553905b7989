import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";

const ResetPasswordSuccessPage: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [countdown, setCountdown] = useState(5);

  // Auto-redirect countdown
  useEffect(() => {
    const interval = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          navigate("/", { replace: true });
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [navigate]);

  const handleGoToSignIn = () => {
    navigate("/", { replace: true });
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40 min-h-screen">
      <div className="w-full sm:max-w-md mx-auto my-auto rounded-2xl bg-white dark:bg-slate-800 border-b-2 border-black/10 p-10 relative shadow-xl flex flex-col text-center">
        <div className="mb-6">
          <span className="text-3xl font-bold text-white dark:text-slate-200 bg-gradient-to-br from-[#1A1919] to-[#64646B] rounded-2xl px-4 py-2 inline-block">Z</span>
        </div>
        <div className="mb-4">
          <div className="text-2xl font-bold textGradient bg-gradient-to-r from-[#191a1d] via-[#888888] to-[#191a1d] bg-clip-text text-transparent mb-2">{t('passwordChangedSuccessfully')}</div>
          <div className="text-sm text-gray-500 dark:text-gray-400 font-normal mb-4 mt-2">
            {t('autoRedirectMessage', { count: countdown })}
          </div>
        </div>
        <button
          type="button"
          className="ButtonSignIn ButtonCreateAccount button-gradient bg-[#222428] dark:bg-slate-700 hover:bg-opacity-90 dark:hover:bg-slate-600 text-white dark:text-gray-300 dark:hover:text-white w-full rounded-lg font-medium text-sm py-2.5 mt-2"
          onClick={handleGoToSignIn}
        >
          {t('goToSignIn')}
        </button>
      </div>
    </div>
  );
};

export default ResetPasswordSuccessPage; 