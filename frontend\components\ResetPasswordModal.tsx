import React from "react";
import { CloseIcon } from "../constants";

interface ResetPasswordModalProps {
  isOpen: boolean;
  onClose: () => void;
  onBackToSignIn: () => void;
  onSendReset: (email: string) => void;
  error?: string | null;
}

const ResetPasswordModal: React.FC<ResetPasswordModalProps> = ({
  isOpen,
  onClose,
  onBackToSignIn,
  onSendReset,
  error,
}) => {
  const [email, setEmail] = React.useState("");
  const [sent, setSent] = React.useState(false);

  if (!isOpen) return null;

  const [isSubmitting, setIsSubmitting] = React.useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (isSubmitting) return;
    
    setIsSubmitting(true);
    try {
      await onSendReset(email);
      setSent(true);
    } catch (err) {
      // Error handling is done in the parent component
      console.error("Error sending reset email:", err);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/40"
      onClick={e => { if (e.target === e.currentTarget) onClose(); }}
      role="dialog"
      aria-modal="true"
    >
      <div className="w-full sm:max-w-md min-h-screen flex flex-col text-center relative">
        <div className="my-auto w-full">
          <div className="dark:text-gray-100 rounded-2xl bg-white dark:bg-slate-800 border-b-2 border-black/10 p-10 relative">
            <button
              onClick={onClose}
              className="absolute top-4 right-4 p-1.5 text-slate-400 dark:text-slate-500 hover:text-slate-600 dark:hover:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-full"
              aria-label="Close"
              type="button"
            >
              <CloseIcon className="w-5 h-5" />
            </button>
            {sent ? (
              <div className="flex flex-col items-center justify-center">
                <div className="self-center mb-6 size-14">
                  <div className="h-full w-full aspect-square rounded-2xl bg-gradient-to-br from-[#1A1919] to-[#64646B] flex justify-center items-center">
                    <span className="text-3xl font-bold text-white dark:text-slate-200">Z</span>
                  </div>
                </div>
                <div className="mb-4">
                  <div className="text-2xl font-bold text-center textGradient bg-gradient-to-r from-[#191a1d] via-[#888888] to-[#191a1d] bg-clip-text text-transparent mb-2">Check Your Email</div>
                  <div className="text-sm text-gray-500 dark:text-gray-400 font-normal mb-4 mt-2">
                    If an account exists for this email, a password reset link has been sent. Please go to your inbox and click the link. In case you can't see the message, check the spam box.
                  </div>
                </div>
                <button
                  type="button"
                  className="ButtonSignIn ButtonCreateAccount button-gradient bg-[#222428] dark:bg-slate-700 hover:bg-opacity-90 dark:hover:bg-slate-600 text-white dark:text-gray-300 dark:hover:text-white w-full rounded-lg font-medium text-sm py-2.5 mb-2"
                  onClick={() => {
                    setEmail("");
                    setSent(false);
                    onBackToSignIn();
                  }}
                >
                  OK
                </button>
              </div>
            ) : (
              <form className="flex flex-col justify-center" onSubmit={handleSubmit}>
                <div className="self-center mb-6 size-14">
                  <div className="h-full w-full aspect-square rounded-2xl bg-gradient-to-br from-[#1A1919] to-[#64646B] flex justify-center items-center">
                    <span className="text-3xl font-bold text-white dark:text-slate-200">Z</span>
                  </div>
                </div>
                <div className="mb-4">
                  <div className="text-2xl font-bold text-center textGradient bg-gradient-to-r from-[#191a1d] via-[#888888] to-[#191a1d] bg-clip-text text-transparent mb-2">Reset Password</div>
                  <div className="text-sm text-gray-500 dark:text-gray-400 font-normal mb-4 mt-2">Enter your email address and we will send you a password reset link</div>
                </div>
                <div className="loginForm flex flex-col">
                  <div className="mb-4">
                    <div className="text-sm font-medium text-left mb-1">Email</div>
                    <div className="flex gap-2 items-center px-3 py-2.5 rounded-lg border border-black/10 dark:border-white/10">
                      {/* Email SVG icon */}
                      <svg className="size-5 opacity-80" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 20 20">
                        <g opacity="0.8">
                          <path d="M1.66664 14.25C1.66664 15.3546 2.56207 16.25 3.66664 16.25H16.3333C17.4379 16.25 18.3333 15.3546 18.3333 14.25V10V5.75C18.3333 4.64543 17.4379 3.75 16.3333 3.75H9.99997H3.66664C2.56207 3.75 1.66664 4.64543 1.66664 5.75V10V14.25Z" strokeLinejoin="round"></path>
                          <path d="M3.00002 4.5L9.99998 10L17 4.5" strokeLinecap="round" strokeLinejoin="round"></path>
                        </g>
                      </svg>
                      <input
                        type="email"
                        className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-slate-700 text-slate-900 dark:text-slate-100 placeholder-slate-400 dark:placeholder-slate-500"
                        autoComplete="email"
                        name="email"
                        placeholder="Enter Your Email"
                        required
                        value={email}
                        onChange={e => setEmail(e.target.value)}
                      />
                    </div>
                  </div>
                  {error && <div className="text-red-500 text-xs mb-2">{error}</div>}
                  <button
                    className="ButtonSignIn ButtonCreateAccount button-gradient bg-[#222428] dark:bg-slate-700 hover:bg-opacity-90 dark:hover:bg-slate-600 text-white dark:text-gray-300 dark:hover:text-white w-full rounded-lg font-medium text-sm py-2.5 mb-2 disabled:opacity-50 disabled:cursor-not-allowed"
                    type="submit"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? "Sending..." : "Send Reset Link"}
                  </button>
                  <button
                    type="button"
                    className="ButtonSkipForNow flex justify-center items-center w-full rounded-lg border border-black/10 dark:border-white/10 font-medium text-sm py-2.5 dark:text-gray-300 dark:hover:text-white dark:hover:bg-white/5 bg-white dark:bg-transparent"
                    onClick={onBackToSignIn}
                  >
                    Back to Sign in
                  </button>
                </div>
              </form>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResetPasswordModal;
