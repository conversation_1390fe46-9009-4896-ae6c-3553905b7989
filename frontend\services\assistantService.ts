import { supabase } from '../lib/supabase';

import { Assistant } from '../types/assistant';

export interface DatabaseAssistant {
  id: string;
  user_id: string | null;
  emoji: string;
  name: string;
  description: string;
  instructions: string;
  parameters: Record<string, any>;
  is_system: boolean;
  access_level: 'free' | 'premium' | 'enterprise';
  created_at: string;
  updated_at: string;
}

class AssistantService {
  private static instance: AssistantService;

  static getInstance(): AssistantService {
    if (!AssistantService.instance) {
      AssistantService.instance = new AssistantService();
    }
    return AssistantService.instance;
  }

  /**
   * Get all assistants available to the current user
   * Includes system assistants and user's own assistants
   */
  async getAssistants(): Promise<Assistant[]> {
    try {
      console.log('🔍 [AssistantService] getAssistants called');

      // Try to get from database first
      try {
        // Get current user for future access level filtering
        const { data: { session } } = await supabase.auth.getSession();
        const user = session?.user;
        console.log('🔍 [AssistantService] Current user:', user ? 'authenticated' : 'not authenticated');

        // For now, get all system assistants (future: filter by user access level)
        const { data: assistants, error } = await supabase
          .from('assistants')
          .select('*')
          .or(`is_system.eq.true${user ? `,user_id.eq.${user.id}` : ''}`) // System assistants + user's own
          .order('is_system', { ascending: false }) // System assistants first
          .order('access_level') // Free first, then premium, enterprise
          .order('updated_at', { ascending: false });

        if (error) {
        console.error('[AssistantService] Database error fetching assistants:', {
          error,
          userId: user?.id
        });
          return [];
        }

        console.log(`✅ [AssistantService] Fetched ${assistants?.length || 0} assistants from database`);
        if (assistants && assistants.length > 0) {
          console.log('🔍 [AssistantService] Assistant names:', assistants.map(a => a.name));
          return assistants.map(this.mapDatabaseToAssistant);
        } else {
          console.log('⚠️ [AssistantService] No assistants found in database.');
          return [];
        }
      } catch (dbError) {
      console.error('[AssistantService] Database connection error:', {
        dbError
      });
        return [];
      }
    } catch (error) {
    console.error('[AssistantService] Unexpected error in getAssistants:', {
      error
    });
      return [];
    }
  }

  /**
   * Create a new assistant (admin only - for now just fallback to local storage)
   */
  async createAssistant(assistant: Omit<Assistant, 'id'>): Promise<Assistant | null> {
    try {
      const { data, error } = await supabase
        .from('assistants')
        .insert([{ ...assistant }])
        .select()
        .single();
      if (error) {
      console.error('[AssistantService] Error creating assistant:', {
        error,
        assistant
      });
        return null;
      }
      return this.mapDatabaseToAssistant(data);
    } catch (err) {
    console.error('[AssistantService] Unexpected error creating assistant:', {
      err,
      assistant
    });
      return null;
    }
  }

  /**
   * Update an existing assistant (admin only - for now just fallback to local storage)
   */
  async updateAssistant(assistant: Assistant): Promise<Assistant | null> {
    try {
      const { data, error } = await supabase
        .from('assistants')
        .update({
          emoji: assistant.emoji,
          name: assistant.name,
          description: assistant.description,
          instructions: assistant.instructions,
          parameters: assistant.parameters
        })
        .eq('id', assistant.id)
        .select()
        .single();
      if (error) {
      console.error('[AssistantService] Error updating assistant:', {
        error,
        assistant
      });
        return null;
      }
      return this.mapDatabaseToAssistant(data);
    } catch (err) {
    console.error('[AssistantService] Unexpected error updating assistant:', {
      err,
      assistant
    });
      return null;
    }
  }

  /**
   * Delete an assistant (admin only - for now just fallback to local storage)
   */
  async deleteAssistant(assistantId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('assistants')
        .delete()
        .eq('id', assistantId);
      if (error) {
      console.error('[AssistantService] Error deleting assistant:', {
        error,
        assistantId
      });
        return false;
      }
      return true;
    } catch (err) {
    console.error('[AssistantService] Unexpected error deleting assistant:', {
      err,
      assistantId
    });
      return false;
    }
  }

  /**
   * Get a specific assistant by ID
   */
  async getAssistant(assistantId: string): Promise<Assistant | null> {
    try {
      console.log('🔍 [AssistantService] getAssistant called:', assistantId);

      // First try database
      try {
        const { data: assistant, error } = await supabase
          .from('assistants')
          .select('*')
          .eq('id', assistantId)
          .single();

        if (error) {
        console.error('[AssistantService] Database error fetching assistant:', {
          error,
          assistantId
        });
          return null;
        }

        if (assistant) {
          console.log('✅ [AssistantService] Found assistant in database:', assistant.name);
          return this.mapDatabaseToAssistant(assistant);
        } else {
          console.log('⚠️ [AssistantService] Assistant not found in database.');
          return null;
        }
      } catch (dbError) {
      console.error('[AssistantService] Database connection error:', {
        dbError,
        assistantId
      });
        return null;
      }
    } catch (error) {
    console.error('[AssistantService] Unexpected error in getAssistant:', {
      error,
      assistantId
    });
      return null;
    }
  }

  /**
   * Map database assistant to frontend assistant type
   */
  private mapDatabaseToAssistant(dbAssistant: DatabaseAssistant): Assistant {
    return {
      id: dbAssistant.id,
      emoji: dbAssistant.emoji,
      name: dbAssistant.name,
      description: dbAssistant.description,
      instructions: dbAssistant.instructions,
      parameters: dbAssistant.parameters
    };
  }
}

export const assistantService = AssistantService.getInstance();
