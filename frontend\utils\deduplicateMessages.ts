import { Message } from "../types";
/**
 * Merge two arrays of messages, deduplicating by message id.
 * If a message with the same id exists in both, prefer the one from the first array.
 */
export function deduplicateMessages(primary: Message[], secondary: Message[]): Message[] {
  const seen = new Map<string, Message>();
  for (const msg of [...secondary, ...primary]) {
    if (msg.id && !seen.has(msg.id)) {
      seen.set(msg.id, msg);
    }
  }
  // Return in order: sort by timestamp ascending (oldest first)
  return Array.from(seen.values()).sort((a, b) => {
    const aTime = a.timestamp instanceof Date ? a.timestamp.getTime() : new Date(a.timestamp).getTime();
    const bTime = b.timestamp instanceof Date ? b.timestamp.getTime() : new Date(b.timestamp).getTime();
    return aTime - bTime;
  });
}
