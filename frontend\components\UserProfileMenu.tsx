import React, { useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import { SettingsIcon, ArchiveIcon, SignOutIcon, UsersIcon } from "../constants";
// Define View type locally since it's no longer exported from App.tsx
type View = "chat" | "settings" | "userManagement";

interface UserProfileMenuProps {
  isOpen: boolean;
  onClose: () => void;
  triggerRef: React.RefObject<HTMLElement>;
  menuPosition?: "top-left" | "top-right" | "bottom-left" | "bottom-right";
  triggerButtonId?: string;
  navigateTo: (view: View) => void;
  handleLogout: () => void;
  isAdmin?: boolean;
}

interface MenuItemBase {
  type: "item" | "divider";
  id: string;
}

interface MenuItemAction extends MenuItemBase {
  type: "item";
  textKey: string;
  icon: React.ReactNode;
  action: () => void;
}

interface MenuItemDivider extends MenuItemBase {
  type: "divider";
}

type MenuItem = MenuItemAction | MenuItemDivider;

const UserProfileMenu: React.FC<UserProfileMenuProps> = ({
  isOpen,
  onClose,
  triggerRef,
  menuPosition = "top-left",
  triggerButtonId,
  navigateTo,
  handleLogout,
  isAdmin = false,
}) => {
  const { t } = useTranslation();
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        menuRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        triggerRef.current &&
        !triggerRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen, onClose, triggerRef]);

  if (!isOpen) {
    return null;
  }

  let style: React.CSSProperties = {};

  if (triggerRef.current) {
    const rect = triggerRef.current.getBoundingClientRect();
    const { innerHeight: vh, innerWidth: vw } = window;

    let top: string | undefined;
    let left: string | undefined;
    let right: string | undefined;
    let bottom: string | undefined;

    switch (menuPosition) {
      case "top-left":
        bottom = `${vh - rect.top}px`;
        left = `${rect.left}px`;
        break;
      case "top-right":
        bottom = `${vh - rect.top}px`;
        right = `${vw - rect.right}px`;
        break;
      case "bottom-left":
        top = `${rect.bottom}px`;
        left = `${rect.left}px`;
        break;
      case "bottom-right":
        top = `${rect.bottom}px`;
        right = `${vw - rect.right}px`;
        break;
    }

    if (top !== undefined) style.top = top;
    if (bottom !== undefined) style.bottom = bottom;
    if (left !== undefined) style.left = left;
    if (right !== undefined) style.right = right;
  } else {
    console.warn(
      "[UserProfileMenu] triggerRef.current is null when menu is open. Cannot calculate position based on trigger. Using fallback position.",
    );
    style.top = "50%";
    style.left = "50%";
    style.transform = "translate(-50%, -50%)";
  }

  const menuItems: MenuItem[] = [
    {
      id: "settings",
      type: "item",
      textKey: "settings",
      icon: (
        <SettingsIcon
          className="w-5 h-5 mr-3 text-slate-500 dark:text-slate-400"
          data-oid="1ex13u-"
        />
      ),
      action: () => navigateTo("settings"),
    },
    // User Management (admin only)
    ...(isAdmin ? [{
      id: "users",
      type: "item" as const,
      textKey: "userManagement",
      icon: (
        <UsersIcon
          className="w-5 h-5 mr-3 text-slate-500 dark:text-slate-400"
        />
      ),
      action: () => navigateTo("userManagement"),
    }] : []),
    {
      id: "archived",
      type: "item",
      textKey: "archivedChats",
      icon: (
        <ArchiveIcon
          className="w-5 h-5 mr-3 text-slate-500 dark:text-slate-400"
          data-oid="i0s:6f."
        />
      ),
      action: () => console.log("Archived Chats clicked"),
    },
    { id: "divider-1", type: "divider" },
    {
      id: "signout",
      type: "item",
      textKey: "logOut",
      icon: (
        <SignOutIcon
          className="w-5 h-5 mr-3 text-slate-500 dark:text-slate-400"
          data-oid="_tij:h6"
        />
      ),
      action: handleLogout,
    },
  ];

  const menuClasses = `fixed bg-white dark:bg-slate-700 rounded-md shadow-lg ring-1 ring-black dark:ring-slate-600 ring-opacity-5 z-[60] w-60 py-1 opacity-100 transition-opacity duration-150 ease-out`;

  return (
    <div
      ref={menuRef}
      style={style}
      className={menuClasses}
      role="menu"
      aria-orientation="vertical"
      aria-labelledby={triggerButtonId}
      data-oid=".u0889l"
    >
      {menuItems.map((item) => {
        if (item.type === "divider") {
          return (
            <div
              key={item.id}
              className="my-1 h-px bg-slate-200 dark:bg-slate-600 mx-4"
              role="separator"
              data-oid="om.2wlp"
            ></div>
          );
        }
        return (
          <button
            key={item.id}
            onClick={() => {
              item.action();
              onClose();
            }}
            className="flex items-center w-full px-4 py-2.5 text-sm text-slate-700 dark:text-slate-200 hover:bg-slate-100 dark:hover:bg-slate-600 hover:text-slate-900 dark:hover:text-slate-100"
            role="menuitem"
            data-oid="jilvlg-"
          >
            {item.icon}
            {t(item.textKey)}
          </button>
        );
      })}
    </div>
  );
};

export default UserProfileMenu;
