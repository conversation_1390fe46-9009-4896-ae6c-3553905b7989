import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Assistant } from "../types/assistant";
import { assistantService } from "../services/assistantService";
import { User } from "../types";

import { PASSelector } from "./PASSelector";

interface WelcomeScreenProps {
  selectedAssistantId: string | null;
  currentUser: User | null;
  isAuthenticated: boolean;
}

const WelcomeScreen: React.FC<WelcomeScreenProps> = ({
  selectedAssistantId,
  currentUser,
  isAuthenticated,
}) => {
  const { t } = useTranslation();
  const [selectedAssistant, setSelectedAssistant] = useState<Assistant | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Handler for PAS assessment selection
  const handleSelectAssessment = (assessmentId: string) => {
    // TODO: Launch assessment dialog/flow for selected assessment
    // For now, just log
    console.log('Selected assessment:', assessmentId);
  };

  // Load assistant details when selectedAssistantId changes
  useEffect(() => {
    const loadAssistant = async () => {
      if (selectedAssistantId) {
        setIsLoading(true);
        try {
          const assistant = await assistantService.getAssistant(selectedAssistantId);
          setSelectedAssistant(assistant);
        } catch (error) {
          console.error('Error loading assistant:', error);
          setSelectedAssistant(null);
        } finally {
          setIsLoading(false);
        }
      } else {
        setSelectedAssistant(null);
      }
    };

    loadAssistant();
  }, [selectedAssistantId]);

  // Get welcome message based on selected assistant
  const getWelcomeMessage = () => {
    if (!isAuthenticated) {
      return {
        greeting: t("welcome", "Welcome to AI Chat"),
        subtitle: t("signInToStart", "Sign in to start chatting with AI assistants"),
        emoji: "👋"
      };
    }

    const userName = currentUser?.name || "there";

    if (isLoading) {
      return {
        greeting: `Hi, ${userName}!`,
        subtitle: "Loading assistant...",
        emoji: "⏳"
      };
    }

    if (selectedAssistant) {
      // Custom welcome messages for each assistant
      const assistantWelcomes: Record<string, { greeting: string; subtitle: string }> = {
        "Default Assistant": {
          greeting: `Hi, ${userName}! I'm your AI Assistant.`,
          subtitle: "I'm here to help with general questions and tasks. What can I assist you with today?"
        },
        "Therapist Assistant": {
          greeting: `Hello, ${userName}. I'm your Therapist Assistant.`,
          subtitle: "I'm here to provide a safe space for you to explore your thoughts and feelings. How are you doing today?"
        },
        "Business Advisor": {
          greeting: `Good day, ${userName}! I'm your Business Advisor.`,
          subtitle: "I'm here to help with business strategy, entrepreneurship, and professional development. What business challenge can I help you tackle?"
        },
        "Learning Tutor": {
          greeting: `Hello, ${userName}! I'm your Learning Tutor.`,
          subtitle: "I'm excited to help you learn something new today! What subject or topic would you like to explore?"
        },
        "Code Assistant": {
          greeting: `Hey, ${userName}! I'm your Code Assistant.`,
          subtitle: "Ready to dive into some coding? I can help with programming questions, debugging, and best practices. What are you working on?"
        }
      };

      const welcome = assistantWelcomes[selectedAssistant.name] || {
        greeting: `Hi, ${userName}! I'm ${selectedAssistant.name}.`,
        subtitle: selectedAssistant.description
      };

      return {
        ...welcome,
        emoji: selectedAssistant.emoji
      };
    }

    // Default welcome when no assistant is selected
    return {
      greeting: `Hi, ${userName}! Welcome to AI Chat.`,
      subtitle: "Select an assistant from the sidebar to get started, or just start typing to chat with the default assistant.",
      emoji: "🤖"
    };
  };

  const { greeting, subtitle, emoji } = getWelcomeMessage();

  return (
    <div className="flex-1 flex items-center justify-center p-8">
      <div className="max-w-2xl mx-auto text-center">
        {/* PASSelector: Assessment Cards */}
        <PASSelector onSelectAssessment={handleSelectAssessment} />
        {/* Assistant Emoji */}
        <div className="mb-6">
          <span className="text-6xl">{emoji}</span>
        </div>

        {/* Greeting */}
        <h1 className="text-3xl font-bold text-slate-900 dark:text-slate-100 mb-4">
          {greeting}
        </h1>

        {/* Subtitle */}
        <p className="text-lg text-slate-600 dark:text-slate-400 mb-8 leading-relaxed">
          {subtitle}
        </p>

        {/* Assistant Info (if selected) */}
        {selectedAssistant && isAuthenticated && (
          <div className="bg-slate-50 dark:bg-slate-800 rounded-lg p-6 mb-6">
            <div className="flex items-center justify-center mb-3">
              <span className="text-2xl mr-3">{selectedAssistant.emoji}</span>
              <h3 className="text-xl font-semibold text-slate-900 dark:text-slate-100">
                {selectedAssistant.name}
              </h3>
            </div>
            <p className="text-slate-600 dark:text-slate-400">
              {selectedAssistant.description}
            </p>
          </div>
        )}

        {/* Call to Action */}
        {isAuthenticated && (
          <div className="text-slate-500 dark:text-slate-500 text-sm">
            <p>💬 Start typing your message below to begin the conversation</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default WelcomeScreen;
