export enum LogLevel {
  DEBUG,
  INFO,
  SUCCESS,
  WARN,
  ERROR,
}

const LOG_PREFIX = '[ChatApp]';

// Basic styling for console logs in the browser
const STYLES: { [key in LogLevel]: string } = {
  [LogLevel.DEBUG]: 'color: gray;',
  [LogLevel.INFO]: 'color: blue;',
  [LogLevel.SUCCESS]: 'color: green; font-weight: bold;',
  [LogLevel.WARN]: 'color: orange;',
  [LogLevel.ERROR]: 'color: red; font-weight: bold;',
};

/**
 * A simple logger utility to standardize console output.
 * @param level The severity level of the log.
 * @param message The main message to log.
 * @param args Additional arguments or objects to log.
 */
export const log = (level: LogLevel, message: string, ...args: any[]) => {
  // In a production environment, you might want to disable lower-level logs
  // For example: if (process.env.NODE_ENV === 'production' && level < LogLevel.WARN) return;

  const style = STYLES[level] || '';
  const levelName = LogLevel[level] || 'LOG';

  const formattedMessage = `%c${LOG_PREFIX} ${message}`;

  switch (level) {
    case LogLevel.ERROR:
      console.error(formattedMessage, style, ...args);
      break;
    case LogLevel.WARN:
      console.warn(formattedMessage, style, ...args);
      break;
    default:
      console.log(formattedMessage, style, ...args);
      break;
  }
};

