#!/usr/bin/env node

/**
 * DEPRECATED: Assistant Mi<PERSON> Script for AI Chat Full Stack
 *
 * This script is deprecated. Use setup-assistants.js instead.
 *
 * The new approach makes assistants global (available to all users)
 * instead of user-specific.
 */

console.log('⚠️  DEPRECATED: This script is deprecated.');
console.log('🔄 Please use setup-assistants.js instead.');
console.log('');
console.log('The new approach:');
console.log('- Makes assistants global (available to all users)');
console.log('- Removes user-specific assistant creation');
console.log('- Provides better psychological assistant templates');
console.log('');
console.log('Run: node setup-assistants.js');
process.exit(1);

const { createClient } = require('@supabase/supabase-js');

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_KEY;

// Validate environment variables
if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
    console.error('❌ Error: Missing environment variables');
    console.error('Please set SUPABASE_URL and SUPABASE_SERVICE_KEY');
    console.error('Example:');
    console.error('export SUPABASE_URL="https://your-project.supabase.co"');
    console.error('export SUPABASE_SERVICE_KEY="your-service-role-key"');
    process.exit(1);
}

// Initialize Supabase client with service role key (bypasses RLS)
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY, {
    auth: {
        autoRefreshToken: false,
        persistSession: false
    }
});

// Default assistants to create for each user
const DEFAULT_ASSISTANTS = [
    {
        emoji: '🤖',
        name: 'Default Assistant',
        description: 'A helpful AI assistant for general tasks.',
        instructions: 'You are a helpful and friendly AI assistant. Be concise, accurate, and supportive in your responses.',
        parameters: { temperature: 0.7, top_p: 1.0 }
    },
    {
        emoji: '🧠',
        name: 'Therapist Assistant',
        description: 'A compassionate AI assistant specialized in providing psychological support and guidance.',
        instructions: 'You are a compassionate and empathetic AI therapist. Provide supportive, non-judgmental responses that help users explore their feelings and thoughts. Always encourage professional help when appropriate and never provide medical diagnoses.',
        parameters: { temperature: 0.8, top_p: 0.9 }
    },
    {
        emoji: '💼',
        name: 'Business Advisor',
        description: 'An AI assistant focused on business strategy, entrepreneurship, and professional development.',
        instructions: 'You are a knowledgeable business advisor with expertise in strategy, entrepreneurship, and professional development. Provide practical, actionable advice while being direct and results-oriented.',
        parameters: { temperature: 0.6, top_p: 0.8 }
    },
    {
        emoji: '🎓',
        name: 'Learning Tutor',
        description: 'An educational AI assistant that helps with learning and academic support.',
        instructions: 'You are a patient and encouraging tutor. Break down complex topics into understandable parts, provide examples, and adapt your teaching style to help users learn effectively. Always encourage curiosity and critical thinking.',
        parameters: { temperature: 0.7, top_p: 0.85 }
    },
    {
        emoji: '💻',
        name: 'Code Assistant',
        description: 'A technical AI assistant specialized in programming and software development.',
        instructions: 'You are an expert software developer and coding mentor. Provide clear, well-commented code examples, explain programming concepts thoroughly, and help debug issues. Focus on best practices and clean code principles.',
        parameters: { temperature: 0.5, top_p: 0.8 }
    }
];

async function getExistingUsers() {
    console.log('👥 Fetching existing users...');
    
    const { data: users, error } = await supabase
        .from('profiles')
        .select('id, name, email');

    if (error) {
        console.error('❌ Error fetching users:', error.message);
        return [];
    }

    console.log(`✅ Found ${users?.length || 0} existing users`);
    return users || [];
}

async function createAssistantsForUser(userId, userName) {
    console.log(`🤖 Creating assistants for user: ${userName}`);
    
    // Check if user already has assistants
    const { data: existingAssistants, error: checkError } = await supabase
        .from('assistants')
        .select('id')
        .eq('user_id', userId);

    if (checkError) {
        console.error(`❌ Error checking existing assistants for ${userName}:`, checkError.message);
        return false;
    }

    if (existingAssistants && existingAssistants.length > 0) {
        console.log(`  ⏭️  User ${userName} already has ${existingAssistants.length} assistants, skipping`);
        return true;
    }

    // Create assistants for this user
    const assistantsToCreate = DEFAULT_ASSISTANTS.map(assistant => ({
        user_id: userId,
        emoji: assistant.emoji,
        name: assistant.name,
        description: assistant.description,
        instructions: assistant.instructions,
        parameters: assistant.parameters,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
    }));

    const { error: insertError } = await supabase
        .from('assistants')
        .insert(assistantsToCreate);

    if (insertError) {
        console.error(`❌ Error creating assistants for ${userName}:`, insertError.message);
        return false;
    }

    console.log(`  ✅ Created ${DEFAULT_ASSISTANTS.length} assistants for ${userName}`);
    return true;
}

async function verifyMigration() {
    console.log('🔍 Verifying assistant migration...');
    
    const { data: assistantStats, error } = await supabase
        .from('assistants')
        .select('user_id, name')
        .order('user_id');

    if (error) {
        console.error('❌ Error verifying migration:', error.message);
        return false;
    }

    // Group by user
    const userAssistants = {};
    assistantStats?.forEach(assistant => {
        if (!userAssistants[assistant.user_id]) {
            userAssistants[assistant.user_id] = [];
        }
        userAssistants[assistant.user_id].push(assistant.name);
    });

    console.log('✅ Migration verification:');
    Object.keys(userAssistants).forEach(userId => {
        console.log(`  - User ${userId}: ${userAssistants[userId].length} assistants`);
    });

    return true;
}

async function main() {
    console.log('🚀 Starting assistant migration...\n');

    try {
        // Step 1: Get all existing users
        const users = await getExistingUsers();
        if (users.length === 0) {
            console.log('ℹ️  No existing users found. Migration complete.');
            return;
        }

        // Step 2: Create assistants for each user
        let successCount = 0;
        for (const user of users) {
            const success = await createAssistantsForUser(user.id, user.name || user.email);
            if (success) {
                successCount++;
            }
        }

        console.log(`\n📊 Migration summary:`);
        console.log(`  - Total users: ${users.length}`);
        console.log(`  - Successfully migrated: ${successCount}`);
        console.log(`  - Failed: ${users.length - successCount}`);

        // Step 3: Verify migration
        await verifyMigration();

        console.log('\n🎉 Assistant migration completed successfully!');
        console.log('\n📋 Next steps:');
        console.log('1. Test the assistant functionality in the frontend');
        console.log('2. Verify that users can create, edit, and delete assistants');
        console.log('3. Test assistant selection and context in chat conversations');

    } catch (error) {
        console.error('❌ Migration failed:', error.message);
        process.exit(1);
    }
}

// Run the migration
if (require.main === module) {
    main();
}

module.exports = { main };
