// Test Supabase connection
// Run this with: npx tsx test-supabase.ts
import 'dotenv/config'
import { supabase, supabaseHelpers } from './lib/supabase'

async function testSupabaseConnection() {
  console.log('🧪 Testing Supabase connection...\n')

  try {
    // Test 1: Check environment variables
    console.log('1. Environment Variables:')
    console.log('   VITE_SUPABASE_URL:', import.meta.env.VITE_SUPABASE_URL ? '✅ Set' : '❌ Missing')
    console.log('   VITE_SUPABASE_ANON_KEY:', import.meta.env.VITE_SUPABASE_ANON_KEY ? '✅ Set' : '❌ Missing')
    console.log('   VITE_GEMINI_API_KEY:', import.meta.env.VITE_GEMINI_API_KEY ? '✅ Set' : '❌ Missing')
    console.log()

    // Test 2: Basic connection
    console.log('2. Testing basic connection...')
    const { data: connectionTest, error: connectionError } = await supabase
      .from('profiles')
      .select('count')
      .limit(1)

    if (connectionError) {
      console.log('   ❌ Connection failed:', connectionError.message)
      return
    } else {
      console.log('   ✅ Connection successful!')
    }
    console.log()

    // Test 3: Check migrated data
    console.log('3. Checking migrated data...')
    
    // Check profiles
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('*')
    
    if (profilesError) {
      console.log('   ❌ Error fetching profiles:', profilesError.message)
    } else {
      console.log(`   ✅ Profiles found: ${profiles?.length || 0}`)
      if (profiles && profiles.length > 0) {
        console.log(`      - ${profiles[0].name} (${profiles[0].email})`)
      }
    }

    // Check chat sessions
    const { data: chatSessions, error: chatsError } = await supabase
      .from('chat_sessions')
      .select('*')
    
    if (chatsError) {
      console.log('   ❌ Error fetching chat sessions:', chatsError.message)
    } else {
      console.log(`   ✅ Chat sessions found: ${chatSessions?.length || 0}`)
      if (chatSessions && chatSessions.length > 0) {
        console.log(`      - "${chatSessions[0].title}"`)
      }
    }

    // Check messages
    const { data: messages, error: messagesError } = await supabase
      .from('messages')
      .select('*')
    
    if (messagesError) {
      console.log('   ❌ Error fetching messages:', messagesError.message)
    } else {
      console.log(`   ✅ Messages found: ${messages?.length || 0}`)
    }
    console.log()

    // Test 4: Test helper functions
    console.log('4. Testing helper functions...')
    
    if (profiles && profiles.length > 0) {
      const userId = profiles[0].id
      
      // Test getting chat sessions for user
      const { data: userChats, error: userChatsError } = await supabaseHelpers.getChatSessions(userId)
      
      if (userChatsError) {
        console.log('   ❌ Error with getChatSessions helper:', userChatsError.message)
      } else {
        console.log(`   ✅ getChatSessions helper works: ${userChats?.length || 0} chats`)
      }

      // Test getting messages for a chat
      if (userChats && userChats.length > 0) {
        const { data: chatMessages, error: chatMessagesError } = await supabaseHelpers.getMessages(userChats[0].id)
        
        if (chatMessagesError) {
          console.log('   ❌ Error with getMessages helper:', chatMessagesError.message)
        } else {
          console.log(`   ✅ getMessages helper works: ${chatMessages?.length || 0} messages`)
        }
      }
    }

    console.log('\n🎉 All tests completed successfully!')
    console.log('\n📋 Next steps:')
    console.log('1. Update React components to use Supabase')
    console.log('2. Replace JSON file fetching with Supabase queries')
    console.log('3. Test authentication flow')

  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

// Run the test
testSupabaseConnection()
