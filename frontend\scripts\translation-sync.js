#!/usr/bin/env node

/**
 * Translation Sync Script
 * 
 * This script helps maintain multilingual support by:
 * 1. Finding missing translation keys across languages
 * 2. Adding placeholder translations for missing keys
 * 3. Reporting translation coverage
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const LOCALES_DIR = path.join(__dirname, '../locales');
const SUPPORTED_LANGUAGES = ['en', 'ru'];

// Load translation files
function loadTranslations() {
  const translations = {};
  
  for (const lang of SUPPORTED_LANGUAGES) {
    const filePath = path.join(LOCALES_DIR, lang, 'translation.json');
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      translations[lang] = JSON.parse(content);
    } catch (error) {
      console.error(`Error loading ${lang} translations:`, error.message);
      translations[lang] = {};
    }
  }
  
  return translations;
}

// Save translation file
function saveTranslations(lang, translations) {
  const filePath = path.join(LOCALES_DIR, lang, 'translation.json');
  const content = JSON.stringify(translations, null, 2) + '\n';
  fs.writeFileSync(filePath, content, 'utf8');
}

// Get all unique keys from all languages
function getAllKeys(translations) {
  const allKeys = new Set();
  
  for (const lang of SUPPORTED_LANGUAGES) {
    Object.keys(translations[lang] || {}).forEach(key => allKeys.add(key));
  }
  
  return Array.from(allKeys).sort();
}

// Find missing keys for each language
function findMissingKeys(translations) {
  const allKeys = getAllKeys(translations);
  const missing = {};
  
  for (const lang of SUPPORTED_LANGUAGES) {
    missing[lang] = allKeys.filter(key => !(key in (translations[lang] || {})));
  }
  
  return missing;
}

// Add missing keys with placeholder values
function addMissingKeys(translations, missingKeys) {
  const updated = { ...translations };
  
  for (const lang of SUPPORTED_LANGUAGES) {
    if (missingKeys[lang].length > 0) {
      updated[lang] = { ...updated[lang] };
      
      for (const key of missingKeys[lang]) {
        // Use English as fallback, or create a placeholder
        const fallback = translations.en?.[key] || `[${key}]`;
        updated[lang][key] = lang === 'en' ? fallback : `[${lang.toUpperCase()}] ${fallback}`;
      }
    }
  }
  
  return updated;
}

// Generate translation report
function generateReport(translations, missingKeys) {
  const allKeys = getAllKeys(translations);
  const totalKeys = allKeys.length;
  
  console.log('\n📊 Translation Coverage Report');
  console.log('================================');
  
  for (const lang of SUPPORTED_LANGUAGES) {
    const existing = Object.keys(translations[lang] || {}).length;
    const missing = missingKeys[lang].length;
    const coverage = totalKeys > 0 ? ((existing / totalKeys) * 100).toFixed(1) : '0.0';
    
    console.log(`${lang.toUpperCase()}: ${existing}/${totalKeys} keys (${coverage}% complete)`);
    
    if (missing > 0) {
      console.log(`  Missing: ${missingKeys[lang].join(', ')}`);
    }
  }
  
  console.log('');
}

// Main function
function main() {
  const command = process.argv[2];
  
  console.log('🌐 Translation Sync Tool');
  console.log('========================');
  
  const translations = loadTranslations();
  const missingKeys = findMissingKeys(translations);
  
  switch (command) {
    case 'check':
      generateReport(translations, missingKeys);
      break;
      
    case 'sync':
      const updated = addMissingKeys(translations, missingKeys);
      
      // Save updated translations
      for (const lang of SUPPORTED_LANGUAGES) {
        if (missingKeys[lang].length > 0) {
          saveTranslations(lang, updated[lang]);
          console.log(`✅ Added ${missingKeys[lang].length} missing keys to ${lang}`);
        }
      }
      
      generateReport(updated, findMissingKeys(updated));
      console.log('🎉 Translation sync complete!');
      console.log('\n💡 Tip: Review the placeholder translations and replace them with proper translations.');
      break;
      
    case 'add':
      const key = process.argv[3];
      const value = process.argv[4];
      
      if (!key || !value) {
        console.log('Usage: npm run translation:add <key> <english_value>');
        console.log('Example: npm run translation:add "welcomeBack" "Welcome back!"');
        return;
      }
      
      // Add to English first
      const updatedEn = { ...translations.en, [key]: value };
      saveTranslations('en', updatedEn);
      
      // Add placeholders to other languages
      for (const lang of SUPPORTED_LANGUAGES) {
        if (lang !== 'en') {
          const updatedLang = { ...translations[lang], [key]: `[${lang.toUpperCase()}] ${value}` };
          saveTranslations(lang, updatedLang);
        }
      }
      
      console.log(`✅ Added translation key "${key}" to all languages`);
      console.log(`📝 Don't forget to translate the placeholder values!`);
      break;
      
    default:
      console.log('Available commands:');
      console.log('  check - Show translation coverage report');
      console.log('  sync  - Add missing keys with placeholders');
      console.log('  add   - Add a new translation key');
      console.log('');
      console.log('Usage:');
      console.log('  npm run translation:check');
      console.log('  npm run translation:sync');
      console.log('  npm run translation:add <key> <english_value>');
  }
}

// Check if this script is being run directly
const isMainModule = import.meta.url.startsWith('file:') && 
  import.meta.url.includes(process.argv[1].replace(/\\/g, '/'));

if (isMainModule) {
  main();
}

export {
  loadTranslations,
  findMissingKeys,
  addMissingKeys,
  generateReport
};