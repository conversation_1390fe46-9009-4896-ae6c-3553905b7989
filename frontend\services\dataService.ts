import { supabase, supabaseHelpers } from '../lib/supabase'
import { ChatSession, Message, User, UserProfiles, Profile } from '../types'
import { chatService } from './chatService'
import { userService } from './userService'

// Data service that handles both Supabase and fallback to JSON files
export class DataService {
  private static instance: DataService
  // No longer needed as we handle fallbacks directly in each method

  static getInstance(): DataService {
    if (!DataService.instance) {
      DataService.instance = new DataService()
    }
    return DataService.instance
  }

  // Check if user is authenticated
  async isAuthenticated(): Promise<boolean> {
    try {
      // Use getSession instead of getUser - it's more reliable
      const { data: { session } } = await supabase.auth.getSession();
      return !!session?.user
    } catch (error) {
      console.error('[DataService] isAuthenticated error:', error);
      return false
    }
  }

  // Get current user - Delegate to UserService
  async getCurrentUser(): Promise<User | null> {
    try {
      console.log('🔍 [DataService] Delegating to UserService.getCurrentUser');
      return await userService.getCurrentUser();
    } catch (error) {
      console.error('❌ [DataService] getCurrentUser error:', error);
      return null;
    }
  }
  
  // Update profile picture
  async updateProfilePicture(file: File): Promise<string | null> {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      const user = session?.user;
      if (!user) return null;
      
      // Use the uploadProfilePicture helper which now returns a data URL
      const { data, error } = await supabaseHelpers.uploadProfilePicture(user.id, file);
      if (error || !data) {
        console.error('[DataService] uploadProfilePicture error:', error);
        return null;
      }
      
      // For data URLs, we can skip updating the profile in the database
      // In a production app, you would update the profile with the URL from Supabase storage
      if (data.url.startsWith('data:')) {
        // For data URLs, we'll just return the URL without updating the profile
        // This is a temporary solution for development/testing
        return data.url;
      } else {
        // For actual URLs (if we switch back to Supabase storage), update the profile
        const { error: updateError } = await supabaseHelpers.updateProfile(user.id, {
          avatar_url: data.url
        });
        
        if (updateError) {
          console.error('[DataService] updateProfile error:', updateError);
          return null;
        }
      }
      
      return data.url;
    } catch (error) {
      console.error('[DataService] updateProfilePicture error:', error);
      return null;
    }
  }
  
  // Update user profile
  async updateUserProfile(updates: Partial<User>): Promise<User | null> {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      const user = session?.user;
      if (!user) return null;
      
      // Map frontend User type to Supabase Profile type
      const profileUpdates: Partial<Profile> = {
        name: updates.name,
        avatar_url: updates.avatarUrl,
        // Don't update email through profile table
      };
      
      const { data, error } = await supabaseHelpers.updateProfile(user.id, profileUpdates);
      if (error || !data) {
        console.error('[DataService] updateProfile error:', error);
        return null;
      }
      
      // If email update is requested, update auth email
      if (updates.email && updates.email !== user.email) {
        const { error: emailError } = await supabase.auth.updateUser({
          email: updates.email
        });
        
        if (emailError) {
          console.error('[DataService] updateEmail error:', emailError);
          return null;
        }
      }
      
      return {
        name: data.name || user.email || '',
        email: data.email || user.email || '',
        avatarUrl: data.avatar_url || ''
      };
    } catch (error) {
      console.error('[DataService] updateUserProfile error:', error);
      return null;
    }
  }
  
  // Change password
  async changePassword(currentPassword: string, newPassword: string): Promise<boolean> {
    try {
      const { error } = await supabaseHelpers.changePassword(currentPassword, newPassword);
      if (error) {
        console.error('[DataService] changePassword error:', error);
        return false;
      }
      return true;
    } catch (error) {
      console.error('[DataService] changePassword error:', error);
      return false;
    }
  }
  
  // User Management Functions - Delegate to UserService
  async getAllUsers() {
    try {
      console.log('🔍 [DataService] Delegating to UserService.getAllUsers');
      return await userService.getAllUsers();
    } catch (error) {
      console.error('❌ [DataService] getAllUsers error:', error);
      return [];
    }
  }
  
  async createUser(name: string, email: string, password: string, role: 'admin' | 'user' = 'user') {
    try {
      // For now, we'll show a message explaining how to create users
      alert(`To create a new user:\n\n1. Go to Supabase Authentication dashboard\n2. Click "Add User"\n3. Enter email: ${email}\n4. Enter password: ${password}\n5. After creating the user, set their role to "${role}" in the profiles table`);
      
      // Return a mock user for UI demonstration
      return {
        id: 'temp-' + Date.now(),
        name,
        email,
        role,
        avatarUrl: '',
        createdAt: new Date(),
        lastActive: new Date()
      };
    } catch (error) {
      console.error('[DataService] createUser error:', error);
      return null;
    }
  }
  
  async updateUser(userId: string, updates: { name?: string, email?: string, role?: 'admin' | 'user', avatarUrl?: string }) {
    try {
      console.log('🔍 [DataService] Delegating to UserService.updateUser');
      return await userService.updateUser(userId, updates);
    } catch (error) {
      console.error('❌ [DataService] updateUser error:', error);
      return null;
    }
  }
  
  async deleteUser(userId: string) {
    try {
      console.log('🔍 [DataService] Delegating to UserService.deleteUser');
      return await userService.deleteUser(userId);
    } catch (error) {
      console.error('❌ [DataService] deleteUser error:', error);
      return false;
    }
  }
  
  async isUserAdmin(): Promise<boolean> {
    try {
      console.log('🔍 [DataService] Delegating to UserService.isUserAdmin');
      return await userService.isUserAdmin();
    } catch (error) {
      console.error('❌ [DataService] isUserAdmin error:', error);
      return false;
    }
  }

  // Chat Management Functions - Delegate to ChatService
  async updateChatTitle(sessionId: string, title: string): Promise<boolean> {
    try {
      console.log('🔍 [DataService] Delegating to ChatService.updateChatTitle');
      return await chatService.updateChatTitle(sessionId, title);
    } catch (error) {
      console.error('❌ [DataService] updateChatTitle error:', error);
      return false;
    }
  }

  async deleteChatSession(sessionId: string): Promise<boolean> {
    try {
      console.log('🔍 [DataService] Delegating to ChatService.deleteChatSession');
      return await chatService.deleteChatSession(sessionId);
    } catch (error) {
      console.error('❌ [DataService] deleteChatSession error:', error);
      return false;
    }
  }

  // Account Management Functions - Delegate to UserService
  async deleteMyAccount(): Promise<boolean> {
    try {
      console.log('🔍 [DataService] Delegating to UserService.deleteMyAccount');
      return await userService.deleteMyAccount();
    } catch (error) {
      console.error('❌ [DataService] deleteMyAccount error:', error);
      return false;
    }
  }

  // Get user profiles (for compatibility with existing code)
  async getUserProfiles(): Promise<UserProfiles | null> {
    try {
      const currentUser = await this.getCurrentUser()
      if (!currentUser) {
        // Return null if not authenticated instead of fallback data
        console.log('🔍 [DataService] No authenticated user, returning null');
        return null;
      }

      return {
        loggedInUser: currentUser,
        guestUser: {
          name: 'Guest',
          avatarUrl: '',
          email: ''
        }
      }
    } catch (error) {
      console.error('❌ [DataService] getUserProfiles error:', error);
      // Return null instead of fallback data to prevent showing hardcoded users
      return null;
    }
  }

  // Get chat sessions - Delegate to ChatService
  async getChatSessions(): Promise<ChatSession[]> {
    try {
      console.log('🔍 [DataService] Delegating to ChatService.getChatSessions');
      return await chatService.getChatSessions();
    } catch (error) {
      console.error('❌ [DataService] getChatSessions error:', error);
      // Return empty array instead of fallback data to prevent showing hardcoded chats
      return [];
    }
  }

  // Load messages for a specific chat session - Delegate to ChatService
  async getMessagesForSession(sessionId: string): Promise<Message[]> {
    try {
      console.log('🔍 [DataService] Delegating to ChatService.getMessagesForSession');
      return await chatService.getMessagesForSession(sessionId);
    } catch (error) {
      console.error('❌ [DataService] getMessagesForSession error:', error);
      return [];
    }
  }

  // Create new chat session - Delegate to ChatService
  async createChatSession(
    title: string,
    iconName: string = 'SpeechBubbleIcon',
    assistantId?: string | null,
    assistantName?: string | null,
    assistantEmoji?: string | null
  ): Promise<ChatSession | null> {
    try {
      console.log('🔍 [DataService] Delegating to ChatService.createChatSession');
      return await chatService.createChatSession(title, iconName, assistantId, assistantName, assistantEmoji);
    } catch (error) {
      console.error('❌ [DataService] createChatSession error:', error);
      return null;
    }
  }

  // Add message to chat session - Delegate to ChatService
  async addMessage(chatSessionId: string, message: Omit<Message, 'id' | 'timestamp'>): Promise<Message | null> {
    try {
      console.log('🔍 [DataService] Delegating to ChatService.addMessage');
      return await chatService.addMessage(chatSessionId, message);
    } catch (error) {
      console.error('❌ [DataService] addMessage error:', error);
      return null;
    }
  }

  // Fallback methods for JSON data
  private async getFallbackUserProfiles(): Promise<UserProfiles | null> {
    try {
      const response = await fetch('./data/users.json')
      if (!response.ok) return null
      return await response.json()
    } catch (error) {
      return null
    }
  }

  private async getFallbackChatSessions(): Promise<ChatSession[]> {
    try {
      const response = await fetch('./data/chats.json')
      if (!response.ok) return []
      const rawChatsData = await response.json()

      return rawChatsData.map((chat: any) => ({
        ...chat,
        messages: chat.messages || [],
        lastActivity: new Date(chat.lastActivity),
      }))
    } catch (error) {
      return []
    }
  }

  // Authentication methods
  async signIn(email: string, password: string) {
    return supabaseHelpers.signIn(email, password)
  }

  async signOut() {
    return supabaseHelpers.signOut()
  }

  // Subscribe to auth changes
  onAuthStateChange(callback: (event: string, session: any) => void) {
    return supabase.auth.onAuthStateChange(callback)
  }

  // Subscribe to real-time chat updates
  subscribeToChatMessages(chatSessionId: string, callback: (payload: any) => void) {
    return supabaseHelpers.subscribeToChatMessages(chatSessionId, callback)
  }

  subscribeToChatSessions(userId: string, callback: (payload: any) => void) {
    return supabaseHelpers.subscribeToChatSessions(userId, callback)
  }
}

// Export singleton instance
export const dataService = DataService.getInstance()
