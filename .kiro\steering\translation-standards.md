---
inclusion: always
---

# Translation Standards

## Always Use Translations

When writing React components, always use the `useTranslation` hook for any user-facing text:

```tsx
import { useTranslation } from 'react-i18next';

function Component() {
  const { t } = useTranslation();
  return <button>{t('buttonText')}</button>;
}
```

## Never Hardcode Text

❌ **Don't do this:**
```tsx
<button>Save Changes</button>
<p>Profile updated successfully</p>
```

✅ **Do this instead:**
```tsx
<button>{t('saveChanges')}</button>
<p>{t('profileUpdateSuccess')}</p>
```

## Adding New Translation Keys

When you need a new translation key:

1. Use the command: `npm run translation:add "keyName" "English Value"`
2. Replace placeholders in other languages with proper translations
3. Use descriptive key names like `profileUpdateSuccess` not `msg1`

## Current Languages

- English (en) - Primary
- Russian (ru) - Secondary

## Translation Commands

- `npm run translation:check` - Check coverage
- `npm run translation:sync` - Sync missing keys
- `npm run translation:add <key> <value>` - Add new key