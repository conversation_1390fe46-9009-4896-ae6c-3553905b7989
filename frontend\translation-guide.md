# Translation Guide

This guide helps you maintain multilingual support during development.

## Current Languages

- **English (en)** - Primary language
- **Russian (ru)** - Secondary language

## 🔄 Recommended Translation Workflow

### **Development-First Approach (Recommended)**

**While coding new components:**
```tsx
// ✅ Add translations as you code
import { useTranslation } from 'react-i18next';

function MyComponent() {
  const { t } = useTranslation();
  
  return (
    <div>
      <h1>{t('newFeatureTitle')}</h1>
      <p>{t('newFeatureDescription')}</p>
      <button>{t('saveButton')}</button>
    </div>
  );
}
```

**Quick add translations:**
```bash
npm run translation:add "newFeatureTitle" "New Feature"
npm run translation:add "newFeatureDescription" "This is a new feature"
npm run translation:add "saveButton" "Save"
```

### **Batch Translation Workflow**

**Option A: Use placeholders first, translate later**
```tsx
// Code with translation keys immediately
<button>{t('submitForm')}</button>
<p>{t('validationError')}</p>
```

Then run periodic translation sessions:
```bash
# Check what needs translation
npm run translation:check

# Sync missing keys (adds placeholders)
npm run translation:sync

# Then replace Russian placeholders with proper translations
```

## Translation Commands

### 1. Adding New Translation Keys

```bash
# Add a new translation key with English value
npm run translation:add "buttonText" "Click Here"
```

This will:
- Add the key to English translations
- Add placeholder translations to other languages
- You can then replace placeholders with proper translations

### 2. Checking Translation Coverage

```bash
# Check which keys are missing in each language
npm run translation:check
```

### 3. Syncing Missing Keys

```bash
# Add placeholder translations for missing keys
npm run translation:sync
```

## Translation File Structure

```
frontend/locales/
├── en/
│   └── translation.json
└── ru/
    └── translation.json
```

## VS Code Integration

Use the built-in snippets for faster development:
- Type `usetrans` → `const { t } = useTranslation();`
- Type `trans` → `t('translationKey')`
- Type `transi` → `t('translationKey', { variable: value })`
- Type `addtrans` → Comment with add command

## Development Schedule

**Recommended workflow:**
- **Daily:** Add translation keys as you code
- **Weekly:** Replace Russian placeholders with proper translations  
- **Before releases:** Run full translation audit with `npm run translation:check`

## Best Practices

### ✅ Do:
1. **Always use translation keys** instead of hardcoded text
2. **Use descriptive key names** like `profileUpdateSuccess` instead of `msg1`
3. **Group related keys** with prefixes like `error.`, `button.`, `form.`
4. **Add translation keys immediately** when writing UI text
5. **Test in all languages** before deploying
6. **Replace placeholders** with proper translations as soon as possible
7. **Run `npm run translation:check`** before commits

### ❌ Don't:
1. Leave hardcoded text "temporarily"
2. Use generic key names like `msg1`, `text2`
3. Forget to translate placeholders
4. Skip translation keys thinking you'll add them later

## Placeholder Format

When syncing, placeholders follow this format:
- `[RU] English text` for Russian
- `[ES] English text` for Spanish (when added)

## Adding New Languages

To add a new language (e.g., Spanish):

1. Create the directory: `frontend/locales/es/`
2. Create the file: `frontend/locales/es/translation.json`
3. Update `SUPPORTED_LANGUAGES` in `scripts/translation-sync.js`
4. Update the i18n configuration in `frontend/i18n.ts`
5. Run `npm run translation:sync` to populate with placeholders

## Interpolation

For dynamic content, use interpolation:

```json
{
  "welcomeMessage": "Hi, {{name}}"
}
```

```tsx
t('welcomeMessage', { name: user.name })
```

## Pluralization

For plural forms:

```json
{
  "itemCount": "{{count}} item",
  "itemCount_plural": "{{count}} items"
}
```

```tsx
t('itemCount', { count: items.length })
```

## Debugging

Enable i18n debug mode in `frontend/i18n.ts`:
```typescript
debug: true
```

This will log translation loading and missing key warnings to the console.