# Supabase Configuration for Local Development
# Copy this file to .env.local and fill in your actual values
#
# Note: .env.local is used for local development only
# For production deployment on Vercel, set these in Vercel's environment variables dashboard

# Your Supabase project URL (found in Settings > API)
VITE_SUPABASE_URL=https://auimolxombvsvrgxgwhz.supabase.co

# Your Supabase anon/public key (found in Settings > API)
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF1aW1vbHhvbWJ2c3ZyZ3hnd2h6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyMjg3NzMsImV4cCI6MjA2NzgwNDc3M30.vTdBtdAA5dPqNfhH3yMvpKTh7HrIg2A5ZpieltDZzvQ

# Optional: Google Gemini API Key (if you want to keep using Gemini)
VITE_GEMINI_API_KEY=AIzaSyBDog2FNMNdvcebrk_WteAL_9WwaQHgMqg

# Development vs Production Notes:
# - .env.local: Used for local development (ignored by git)
# - Vercel: Set environment variables in Vercel dashboard for production
# - All variables must start with VITE_ to be accessible in the browser

VITE_SUPABASE_SERVICE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF1aW1vbHhvbWJ2c3ZyZ3hnd2h6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjIyODc3MywiZXhwIjoyMDY3ODA0NzczfQ.KVTTpvGhBzAD8RoXTKNV_1d2S0Sy2Pu9Zfjnsq5JZro
