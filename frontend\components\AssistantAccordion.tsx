import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { ChevronDownIcon } from "../constants";
import { Assistant } from "../types/assistant";
import { assistantService } from "../services/assistantService";

interface AssistantAccordionProps {
  isSidebarOpen: boolean;
  selectedAssistantId: string | null;
  onSelectAssistant: (assistantId: string | null) => void;
}

interface AssistantListItemProps {
  assistant: Assistant;
  isSelected: boolean;
  isAssociatedWithCurrentChat: boolean;
  onSelect: () => void;
  isSidebarOpen: boolean;
}

const AssistantListItem: React.FC<AssistantListItemProps> = ({
  assistant,
  isSelected,
  isAssociatedWithCurrentChat,
  onSelect,
  isSidebarOpen,
}) => {
  return (
    <div
      className={`flex items-center w-full px-3 py-2.5 text-sm rounded-md hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors group cursor-pointer
        ${isSelected
          ? "bg-slate-100 dark:bg-slate-700 font-medium text-slate-800 dark:text-slate-100"
          : isAssociatedWithCurrentChat
          ? "bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 font-medium text-blue-800 dark:text-blue-300"
          : "text-slate-600 dark:text-slate-300"
        }`}
      onClick={onSelect}
    >
      <span className="mr-2.5 text-lg">
        {assistant.emoji}
      </span>
      <div className="flex-1 min-w-0">
        <div className="truncate font-medium">
          {assistant.name}
        </div>
        {assistant.description && (
          <div className="text-xs text-slate-500 dark:text-slate-400 truncate">
            {assistant.description}
          </div>
        )}
      </div>
    </div>
  );
};

interface AssistantAccordionProps {
  isSidebarOpen: boolean;
  selectedAssistantId: string | null;
  currentChatAssistantId: string | null;
  onSelectAssistant: (assistantId: string | null) => void;
}

const AssistantAccordion: React.FC<AssistantAccordionProps> = ({
  isSidebarOpen,
  selectedAssistantId,
  currentChatAssistantId,
  onSelectAssistant,
}) => {
  const { t } = useTranslation();
  const [isAssistantsOpen, setIsAssistantsOpen] = useState(true);
  const [assistants, setAssistants] = useState<Assistant[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const toggleAssistants = () => {
    setIsAssistantsOpen((prev) => !prev);
  };

  const loadAssistants = async () => {
    setIsLoading(true);
    try {
      const fetchedAssistants = await assistantService.getAssistants();
      setAssistants(fetchedAssistants);
    } catch (error) {
      console.error('Error loading assistants:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (isSidebarOpen) {
      loadAssistants();
    }
  }, [isSidebarOpen]);

  // No auto-selection - let users manually choose assistants
  // This provides a cleaner UX where users see the welcome screen first

  const handleSelectAssistant = (assistantId: string) => {
    console.log('🔍 [AssistantAccordion] handleSelectAssistant called:', {
      assistantId,
      selectedAssistantId,
      selectedAssistantIdType: typeof selectedAssistantId,
      selectedAssistantIdIsNull: selectedAssistantId === null,
      selectedAssistantIdIsUndefined: selectedAssistantId === undefined
    });

    try {
      // Ensure selectedAssistantId is properly defined (handle undefined case)
      const currentSelectedId = selectedAssistantId ?? null;

      // If the same assistant is selected, deselect it (use no assistant/default behavior)
      if (currentSelectedId === assistantId) {
        console.log('🔍 [AssistantAccordion] Deselecting current assistant');
        onSelectAssistant(null);
      } else {
        console.log('🔍 [AssistantAccordion] Selecting new assistant:', assistantId);
        onSelectAssistant(assistantId);
      }
    } catch (error) {
      console.error('❌ [AssistantAccordion] Error in handleSelectAssistant:', error);
      console.error('❌ [AssistantAccordion] Error details:', {
        error,
        assistantId,
        selectedAssistantId,
        onSelectAssistant: typeof onSelectAssistant
      });
    }
  };

  return (
    <>
      {/* Sticky Accordion Header */}
      <div className="sticky top-0 z-10 bg-white dark:bg-slate-800 px-3 py-2 border-b border-slate-200 dark:border-slate-700">
        <div
          className={`flex items-center justify-between text-sm font-medium text-slate-700 dark:text-slate-200 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-md cursor-pointer transition-opacity duration-150 ${isSidebarOpen ? "opacity-100" : "opacity-0 invisible"}`}
          onClick={toggleAssistants}
          role="button"
          tabIndex={isSidebarOpen ? 0 : -1}
          aria-expanded={isAssistantsOpen}
          aria-controls="assistants-content"
        >
          <span>{t("assistants", "Assistants")}</span>
          <ChevronDownIcon
            className={`w-4 h-4 text-slate-500 dark:text-slate-400 transform transition-transform duration-300 ${isAssistantsOpen ? "rotate-180" : "rotate-0"}`}
          />
        </div>
      </div>
      {/* Scrollable Assistant List */}
      <div
        id="assistants-content"
        className={`flex-1 px-2 space-y-1 overflow-y-auto overflow-x-hidden transition-opacity duration-150 sidebar-scrollbar ${isAssistantsOpen && isSidebarOpen ? "max-h-[200px] opacity-100" : "max-h-0 opacity-0"}`}
        style={{ width: '100%' }}
      >
        <div className="mt-2 space-y-1">
          {/* Loading State */}
          {isLoading && (
            <div className="px-3 py-2 text-xs text-slate-500 dark:text-slate-400">
              {t("loadingAssistants", "Loading assistants...")}
            </div>
          )}

          {/* Available Assistants */}
          {!isLoading && assistants.map((assistant) => (
            <AssistantListItem
              key={assistant.id}
              assistant={assistant}
              isSelected={(selectedAssistantId ?? null) === assistant.id}
              isAssociatedWithCurrentChat={(currentChatAssistantId ?? null) === assistant.id}
              onSelect={() => handleSelectAssistant(assistant.id)}
              isSidebarOpen={isSidebarOpen}
            />
          ))}

          {/* Empty State */}
          {!isLoading && assistants.length === 0 && (
            <div className="px-3 py-2 text-xs text-slate-500 dark:text-slate-400">
              {t("noAssistantsAvailable", "No assistants available")}
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default AssistantAccordion;
