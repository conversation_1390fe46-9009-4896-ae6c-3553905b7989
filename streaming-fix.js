// Simple streaming auto-scroll fix
// Replace the complex useEffect with this simple version:

useEffect(() => {
  if (!messagesContainerRef.current || messages.length === 0) return;

  const container = messagesContainerRef.current;
  const lastMessage = messages[messages.length - 1];

  // Handle new messages
  if (messages.length > lastMessageCountRef.current) {
    lastMessageCountRef.current = messages.length;

    if (lastMessage?.sender === 'user') {
      // User message - scroll to show at top
      setTimeout(() => {
        const userElements = container.querySelectorAll('[data-message-id]');
        const userElement = Array.from(userElements).find(
          el => el.getAttribute('data-message-id') === lastMessage.id
        );
        
        if (userElement) {
          const offsetTop = userElement.offsetTop - 80;
          container.scrollTo({
            top: Math.max(0, offsetTop),
            behavior: 'smooth'
          });
        }
      }, 200);
    } else if (lastMessage?.sender === 'ai') {
      // AI message - scroll to bottom
      setTimeout(() => {
        container.scrollTo({
          top: container.scrollHeight,
          behavior: 'smooth'
        });
      }, 100);
    }
  }

  // Simple streaming auto-scroll
  if (lastMessage?.sender === 'ai' && lastMessage.isLoading) {
    // During streaming - keep scrolling to bottom
    container.scrollTop = container.scrollHeight;
  }
}, [messages]);

// Remove the second useEffect entirely - it's causing conflicts