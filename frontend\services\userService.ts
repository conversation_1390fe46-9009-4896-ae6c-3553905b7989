import { supabase } from '../lib/supabase';
import { User } from '../types';
import { connectionService } from './connectionService';

/**
 * Dedicated User Service - Isolated from other features
 * This service handles ONLY user management operations.
 * It now relies on ConnectionService for robust session handling.
 */
export class UserService {
  private static instance: UserService;
  private userCache: { user: User | null; timestamp: number } | null = null;
  private readonly CACHE_DURATION = 30000; // 30 seconds
  private pendingUserRequest: Promise<User | null> | null = null;

  static getInstance(): UserService {
    if (!UserService.instance) {
      UserService.instance = new UserService();
    }
    return UserService.instance;
  }

  /**
   * Get current authenticated user with caching and request deduplication.
   */
  async getCurrentUser(): Promise<User | null> {
    try {
      // 1. Check a recent cache first for performance
      if (this.userCache && (Date.now() - this.userCache.timestamp) < this.CACHE_DURATION) {
        console.log('🔍 [UserService] Returning cached user');
        return this.userCache.user;
      }

      // 2. If a request is already in flight, await it to prevent race conditions
      if (this.pendingUserRequest) {
        console.log('🔍 [UserService] Returning pending user request');
        return this.pendingUserRequest;
      }

      // 3. Perform a new fetch
      console.log('🔍 [UserService] No cached user, fetching new data...');
      this.pendingUserRequest = this.fetchCurrentUser();

      const user = await this.pendingUserRequest;

      // 4. Cache the result
      this.userCache = {
        user,
        timestamp: Date.now()
      };

      return user;
    } catch (error) {
      console.error('❌ [UserService] getCurrentUser error:', error);
      return null;
    } finally {
      // 5. Clear the pending request promise once it's resolved
      this.pendingUserRequest = null;
    }
  }

  /**
   * Internal method to fetch current user from Supabase.
   * Relies on ConnectionService to ensure a valid session.
   */
  private async fetchCurrentUser(): Promise<User | null> {
    try {
      // Use ConnectionService to handle session checks, timeouts, and caching
      const isAuthenticated = await connectionService.ensureAuthSession();

      if (!isAuthenticated) {
        console.log('⚠️ [UserService] No authenticated session found via ConnectionService.');
        return null;
      }

      // If authenticated, get user and profile data
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.log('⚠️ [UserService] Session valid, but no user object returned.');
        return null;
      }

      console.log('🔍 [UserService] Fetching profile for user:', user.id);
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (error) {
        console.error('❌ [UserService] Error fetching profile:', error);
        return null;
      }

      if (!profile) {
        console.error('❌ [UserService] No profile found for user');
        return null;
      }

      console.log('✅ [UserService] Fetched current user profile:', profile.name);

      return {
        name: profile.name || user.email || '',
        email: profile.email || user.email || '',
        avatarUrl: profile.avatar_url || '',
        role: profile.role || 'user'
      };
    } catch (error) {
      console.error('❌ [UserService] fetchCurrentUser failed:', error);
      return null;
    }
  }

  /**
   * Clear user cache and related auth caches.
   */
  clearUserCache(): void {
    console.log('🔄 [UserService] Clearing user cache and pending requests');
    this.userCache = null;
    this.pendingUserRequest = null;
    // IMPORTANT: Also clear the auth cache in the connection service
    connectionService.clearAuthCache();
  }

  /**
   * Clear all cached data including browser storage.
   */
  clearAllCachedData(): void {
    console.log('🔄 [UserService] Clearing all cached data');
    this.clearUserCache();

    try {
      localStorage.removeItem('userProfiles');
      localStorage.removeItem('currentUser');
      localStorage.removeItem('chatSessions');
      localStorage.removeItem('supabase.auth.token');
      console.log('✅ [UserService] Browser storage cleared');
    } catch (error) {
      console.error('❌ [UserService] Error clearing browser storage:', error);
    }
  }

  /**
   * Force refresh user data.
   */
  async forceRefreshUserData(): Promise<void> {
    console.log('🔄 [UserService] Force refreshing user data');
    this.clearAllCachedData();
    await this.getCurrentUser();
    console.log('✅ [UserService] User data refreshed');
  }

  /**
   * Initialize auth state monitoring for cache management.
   */
  initializeAuthStateMonitoring(): void {
    console.log('🔍 [UserService] Initializing auth state monitoring');
    supabase.auth.onAuthStateChange((event, session) => {
      console.log(`🔄 [UserService] Auth state changed: ${event}`);
      this.clearAllCachedData();

      if (event === 'SIGNED_OUT') {
        console.log('👋 [UserService] User signed out - all cached data cleared');
      } else if (event === 'SIGNED_IN') {
        console.log('👋 [UserService] User signed in - all cached data cleared for fresh data');
      }
    });
  }

  /**
   * Check if current user is an admin.
   */
  async isUserAdmin(): Promise<boolean> {
    try {
      const user = await this.getCurrentUser();
      return user?.role === 'admin';
    } catch (error) {
      console.error('❌ [UserService] isUserAdmin error:', error);
      return false;
    }
  }

  /**
   * Get all users (admin only).
   */
  async getAllUsers() {
    try {
      if (!(await this.isUserAdmin())) {
        console.error('❌ [UserService] Access denied - not admin');
        return [];
      }

      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('❌ [UserService] Error fetching users:', error);
        return [];
      }

      return data.map(user => ({
        id: user.id,
        name: user.name || '',
        email: user.email || '',
        role: user.role || 'user',
        avatarUrl: user.avatar_url || '',
        createdAt: new Date(user.created_at),
        lastActive: user.last_active ? new Date(user.last_active) : new Date(user.created_at)
      }));
    } catch (error) {
      console.error('❌ [UserService] getAllUsers error:', error);
      return [];
    }
  }

  /**
   * Update user (admin only).
   */
  async updateUser(userId: string, updates: { name?: string, email?: string, role?: 'admin' | 'user', avatarUrl?: string }) {
    try {
      if (!(await this.isUserAdmin())) {
        console.error('❌ [UserService] Access denied - not admin');
        return null;
      }

      const profileUpdates: any = {};
      if (updates.name !== undefined) profileUpdates.name = updates.name;
      if (updates.email !== undefined) profileUpdates.email = updates.email;
      if (updates.role !== undefined) profileUpdates.role = updates.role;
      if (updates.avatarUrl !== undefined) profileUpdates.avatar_url = updates.avatarUrl;

      const { error } = await supabase
        .from('profiles')
        .update(profileUpdates)
        .eq('id', userId);

      if (error) {
        console.error('❌ [UserService] Error updating user profile:', error);
        return null;
      }
      
      // Note: Updating user's auth email requires a separate admin call which is not included here for simplicity.
      // This function only updates the 'profiles' table.

      const { data: profile, error: fetchError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (fetchError || !profile) {
        console.error('❌ [UserService] Error fetching updated profile:', fetchError);
        return null;
      }

      console.log('✅ [UserService] User updated successfully');
      return {
        id: profile.id,
        name: profile.name || '',
        email: profile.email || '',
        role: profile.role || 'user',
        avatarUrl: profile.avatar_url || '',
        createdAt: new Date(profile.created_at),
        lastActive: profile.last_active ? new Date(profile.last_active) : new Date(profile.created_at)
      };
    } catch (error) {
      console.error('❌ [UserService] updateUser error:', error);
      return null;
    }
  }

  /**
   * Delete user (admin only).
   */
  async deleteUser(userId: string): Promise<boolean> {
    try {
      if (!(await this.isUserAdmin())) {
        console.error('❌ [UserService] Access denied - not admin');
        return false;
      }

      // Use the database function to properly delete the user, which should handle cascades.
      const { error } = await supabase.rpc('delete_user', { user_id: userId });

      if (error) {
        console.error('❌ [UserService] Error deleting user:', error);
        return false;
      }

      console.log('✅ [UserService] User deleted successfully');
      return true;
    } catch (error) {
      console.error('❌ [UserService] deleteUser error:', error);
      return false;
    }
  }

  /**
   * Delete current user's own account (self-deletion).
   */
  async deleteMyAccount(): Promise<boolean> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.error('❌ [UserService] No authenticated user for account deletion');
        return false;
      }

      console.log('🔍 [UserService] Deleting account for user:', user.id);
      
      // Use the database function to properly delete the user.
      const { error } = await supabase.rpc('delete_user', { user_id: user.id });

      if (error) {
        console.error('❌ [UserService] Error deleting own account:', error);
        return false;
      }

      // Clear local state and sign out
      await supabase.auth.signOut();
      this.clearAllCachedData();

      console.log('✅ [UserService] Account deleted successfully');
      return true;
    } catch (error) {
      console.error('❌ [UserService] deleteMyAccount error:', error);
      return false;
    }
  }
}

// Export singleton instance
export const userService = UserService.getInstance();
