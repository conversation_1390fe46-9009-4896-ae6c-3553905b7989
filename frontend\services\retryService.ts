import { log, LogLevel } from '../utils/logger';

/**
 * Configuration options for retry operations
 */
export interface RetryOptions {
  /** Maximum number of retry attempts (default: 3) */
  maxRetries?: number;
  /** Initial delay in milliseconds (default: 1000) */
  initialDelayMs?: number;
  /** Maximum delay in milliseconds (default: 30000) */
  maxDelayMs?: number;
  /** Backoff factor for exponential delay calculation (default: 2) */
  backoffFactor?: number;
  /** Whether to add jitter to delay times to prevent thundering herd (default: true) */
  useJitter?: boolean;
  /** Optional callback to execute before each retry attempt */
  onRetry?: (attempt: number, delay: number, error: any) => void;
}

/**
 * Default retry configuration
 */
const DEFAULT_RETRY_OPTIONS: Required<RetryOptions> = {
  maxRetries: 3,
  initialDelayMs: 1000,
  maxDelayMs: 30000,
  backoffFactor: 2,
  useJitter: true,
  onRetry: (attempt, delay, error) => {
    log(LogLevel.WARN, `🔄 [RetryService] Retry attempt ${attempt} after ${delay}ms. Error: ${error?.message || 'Unknown error'}`);
  }
};

/**
 * Service for handling retry logic with exponential backoff
 */
export class RetryService {
  private static instance: RetryService;

  private constructor() {
    log(LogLevel.INFO, '[RetryService] Initializing...');
  }

  public static getInstance(): RetryService {
    if (!RetryService.instance) {
      RetryService.instance = new RetryService();
    }
    return RetryService.instance;
  }

  /**
   * Executes an operation with retry logic using exponential backoff
   * @param operation The async operation to execute with retry logic
   * @param operationName A name for the operation (for logging)
   * @param options Retry configuration options
   * @returns The result of the operation or null if all retries fail
   */
  public async executeWithRetry<T>(
    operation: () => Promise<T>,
    operationName: string,
    options: RetryOptions = {}
  ): Promise<T | null> {
    // Merge provided options with defaults
    const config: Required<RetryOptions> = {
      ...DEFAULT_RETRY_OPTIONS,
      ...options
    };

    let attempt = 0;
    let lastError: any = null;

    while (attempt <= config.maxRetries) {
      try {
        if (attempt > 0) {
          log(LogLevel.INFO, `🔄 [RetryService] Retry attempt ${attempt}/${config.maxRetries} for ${operationName}`);
        } else {
          log(LogLevel.INFO, `🚀 [RetryService] Executing ${operationName}`);
        }

        // Execute the operation
        const result = await operation();
        
        if (attempt > 0) {
          log(LogLevel.SUCCESS, `✅ [RetryService] ${operationName} succeeded after ${attempt} retries`);
        }
        
        return result;
      } catch (error) {
        lastError = error;
        attempt++;

        // If we've exhausted all retries, break out of the loop
        if (attempt > config.maxRetries) {
          break;
        }

        // Calculate delay with exponential backoff
        const baseDelay = config.initialDelayMs * Math.pow(config.backoffFactor, attempt - 1);
        let delay = Math.min(baseDelay, config.maxDelayMs);
        
        // Add jitter if enabled (±25% randomization)
        if (config.useJitter) {
          const jitterFactor = 0.75 + (Math.random() * 0.5); // Random value between 0.75 and 1.25
          delay = Math.floor(delay * jitterFactor);
        }

        // Execute the onRetry callback if provided
        if (config.onRetry) {
          config.onRetry(attempt, delay, error);
        }

        // Wait before the next retry attempt
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    // If we get here, all retries have failed
    log(LogLevel.ERROR, `❌ [RetryService] ${operationName} failed after ${config.maxRetries} retries. Last error:`, lastError);
    return null;
  }

  /**
   * Determines if an error is retryable based on its type and properties
   * @param error The error to check
   * @returns True if the error is considered retryable
   */
  public isRetryableError(error: any): boolean {
    // Network errors are generally retryable
    if (error instanceof TypeError && error.message.includes('network')) {
      return true;
    }

    // Timeout errors are retryable
    if (error instanceof Error && error.message.includes('timeout')) {
      return true;
    }

    // Check for common HTTP status codes that indicate retryable errors
    if (error?.status) {
      const status = error.status;
      // 408 Request Timeout, 429 Too Many Requests, 5xx Server Errors
      return status === 408 || status === 429 || (status >= 500 && status < 600);
    }

    // Supabase specific error handling
    if (error?.code) {
      // PostgreSQL error codes that might be retryable
      const retryableCodes = [
        '40001', // serialization_failure
        '40P01', // deadlock_detected
        '57P04', // database_dropped
        '57P05'  // database_is_starting
      ];
      return retryableCodes.includes(error.code);
    }

    return false;
  }
}

// Export a singleton instance for use across the application
export const retryService = RetryService.getInstance();