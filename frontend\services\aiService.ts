import { GroundingChunk } from "../types";
import { Assistant } from "../types/assistant";
import { sendMessageToGeminiStream } from "./geminiService";

// Generic AI provider interface
export interface AIProvider {
  name: string;
  sendMessage(
    messageText: string,
    options: AIMessageOptions
  ): AsyncGenerator<AIResponse>;
}

// Unified options for any AI provider
export interface AIMessageOptions {
  useWebSearch?: boolean;
  systemInstruction?: string;
  parameters?: Record<string, any>;
  assistant?: Assistant;
}

// Unified response format
export interface AIResponse {
  textChunk?: string;
  finalGroundingChunks?: GroundingChunk[];
  error?: string;
  isComplete?: boolean;
}

// Gemini provider implementation
class GeminiProvider implements AIProvider {
  name = "gemini";

  async* sendMessage(
    messageText: string,
    options: AIMessageOptions
  ): AsyncGenerator<AIResponse> {
    const { useWebSearch = false, systemInstruction, parameters } = options;
    
    try {
      const stream = sendMessageToGeminiStream(
        messageText,
        useWebSearch,
        systemInstruction,
        parameters
      );

      for await (const chunk of stream) {
        yield {
          textChunk: chunk.textChunk,
          finalGroundingChunks: chunk.finalGroundingChunks,
          error: chunk.error,
          isComplete: false
        };
      }
      
      yield { isComplete: true };
    } catch (error) {
      // Enhanced error logging
      console.error('[GeminiProvider] sendMessage fetch error:', {
        error,
        messageText,
        options
      });
      yield {
        error: error instanceof Error ? (error.stack || error.message) : JSON.stringify(error),
        isComplete: true
      };
    }
  }
}

// OpenAI provider implementation (placeholder for future)
class OpenAIProvider implements AIProvider {
  name = "openai";

  async* sendMessage(
    _messageText: string,
    _options: AIMessageOptions
  ): AsyncGenerator<AIResponse> {
    // TODO: Implement OpenAI integration
    yield {
      error: "OpenAI provider not implemented yet",
      isComplete: true
    };
  }
}

// Claude provider implementation (placeholder for future)
class ClaudeProvider implements AIProvider {
  name = "claude";

  async* sendMessage(
    _messageText: string,
    _options: AIMessageOptions
  ): AsyncGenerator<AIResponse> {
    // TODO: Implement Claude integration
    yield {
      error: "Claude provider not implemented yet",
      isComplete: true
    };
  }
}

// Main AI service that manages providers
class AIService {
  private providers: Map<string, AIProvider> = new Map();
  private currentProvider: string = "gemini";

  constructor() {
    // Register available providers
    this.registerProvider(new GeminiProvider());
    this.registerProvider(new OpenAIProvider());
    this.registerProvider(new ClaudeProvider());
  }

  registerProvider(provider: AIProvider) {
    this.providers.set(provider.name, provider);
  }

  setCurrentProvider(providerName: string) {
    if (this.providers.has(providerName)) {
      this.currentProvider = providerName;
    } else {
      throw new Error(`Provider ${providerName} not found`);
    }
  }

  getCurrentProvider(): AIProvider {
    const provider = this.providers.get(this.currentProvider);
    if (!provider) {
      throw new Error(`Current provider ${this.currentProvider} not found`);
    }
    return provider;
  }

  getAvailableProviders(): string[] {
    return Array.from(this.providers.keys());
  }

  /**
   * Send a message using the current AI provider with assistant context
   */
  async* sendMessage(
    messageText: string,
    options: AIMessageOptions = {}
  ): AsyncGenerator<AIResponse> {
    const provider = this.getCurrentProvider();
    
    // If an assistant is provided, merge its context into options
    if (options.assistant) {
      options.systemInstruction = options.assistant.instructions;
      options.parameters = {
        ...options.parameters,
        ...options.assistant.parameters
      };
    }

    console.log(`🤖 [AIService] Using provider: ${provider.name}`);
    if (options.systemInstruction) {
      console.log(`🤖 [AIService] System instruction: ${options.systemInstruction.substring(0, 100)}...`);
    }
    if (options.parameters) {
      console.log(`🤖 [AIService] Parameters:`, options.parameters);
    }

    yield* provider.sendMessage(messageText, options);
  }

  /**
   * Send a message with assistant context
   */
  async* sendMessageWithAssistant(
    messageText: string,
    assistant: Assistant | null,
    useWebSearch: boolean = false
  ): AsyncGenerator<AIResponse> {
    try {
      console.log('🤖 [AIService] sendMessageWithAssistant called:', {
        messageText: messageText.substring(0, 50) + '...',
        assistantName: assistant?.name || 'none',
        useWebSearch
      });

      const options: AIMessageOptions = {
        useWebSearch,
        assistant: assistant || undefined
      };

      yield* this.sendMessage(messageText, options);
    } catch (error) {
      // Enhanced error logging
      console.error('[AIService] sendMessageWithAssistant error:', {
        error,
        messageText,
        assistant,
        useWebSearch
      });
      yield {
        error: error instanceof Error ? (error.stack || error.message) : JSON.stringify(error),
        isComplete: true
      };
    }
  }
}

// Export singleton instance
export const aiService = new AIService();

// Export types for use in components
// Types are already exported above with 'export interface', so this line is not needed and causes conflicts.
