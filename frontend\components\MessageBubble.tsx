import React from 'react';
import { MessageBubbleProps } from './MessageBubbleProps';

const MessageBubble: React.FC<MessageBubbleProps> = ({ message, isUser, isAI, isLoading, isError, timestamp }) => {
  return (
    <div className={`message-bubble ${isUser ? 'user' : 'ai'} ${isLoading ? 'loading' : ''} ${isError ? 'error' : ''}`}>
      <p>{message}</p>
    </div>
  );
};

export default MessageBubble;