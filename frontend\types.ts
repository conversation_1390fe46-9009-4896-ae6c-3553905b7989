

export interface Profile {
  id: string;
  name: string | null;
  email: string | null;
  avatar_url: string | null;
  role?: 'admin' | 'user';
  last_active?: string;
  created_at: string;
  updated_at: string;
}

export interface User {
  name: string;
  avatarUrl: string;
  email?: string;
  role?: 'admin' | 'user';
}

export interface Message {
  id: string;
  text: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  avatar?: string;
  isLoading?: boolean;
  isError?: boolean;
  groundingChunks?: GroundingChunk[];
  chatId?: string;
  savedToDatabase?: boolean;
}

export interface ChatSession {
  id: string;
  title: string;
  icon?: React.ReactNode;
  iconName?: string;
  messages: Message[];
  lastActivity: Date;
  assistantId?: string | null;
  assistantName?: string | null;
  assistantEmoji?: string | null;
}

// Refined RawChatSession: 'messages' is inherited from ChatSession as it's not in Omit
export interface RawChatSession extends Omit<ChatSession, 'lastActivity' | 'icon'> {
  lastActivity: string; // Override lastActivity to be string for JSON
  iconName?: string;     // Add iconName as icon ReactNode cannot be in JSON
  // messages: Message[]; // This line is removed as 'messages' is inherited and not Omitted
}

export interface Model {
  id: string;
  name: string;
}

export interface GroundingChunk {
  web?: {
    uri: string;
    title: string;
  };
  retrievedContext?: {
    uri: string;
    title: string;
  };
}

export interface UserProfiles {
    loggedInUser: User;
    guestUser: User;
}

export type View = 'chat' | 'settings';
export type AuthView = 'signIn' | 'chatHistoryAccess' | null;
export type SettingsSubView = 'main' | 'account';
export type Theme = 'light' | 'dark' | 'system';