import { supabase } from '../lib/supabase';
import { ChatSession, Message } from '../types';
import { connectionService } from './connectionService';

/**
 * Dedicated Chat Service - Isolated from other features
 * This service handles ONLY chat-related operations
 */
export class ChatService {
  private static instance: ChatService;

  static getInstance(): ChatService {
    if (!ChatService.instance) {
      ChatService.instance = new ChatService();
    }
    return ChatService.instance;
  }

  /**
   * Get all chat sessions for the current user
   */
  async getChatSessions(): Promise<ChatSession[]> {
    const result = await connectionService.executeWithRetry(async () => {
      console.log('🔍 [ChatService] getChatSessions called');
      
      // Ensure auth session is valid
      const hasValidSession = await connectionService.ensureAuthSession();
      if (!hasValidSession) {
        console.log('🔍 [ChatService] No valid authenticated session');
        return [];
      }

      const { data: { session } } = await supabase.auth.getSession();
      const user = session?.user;
      
      if (!user) {
        console.log('🔍 [ChatService] No authenticated user');
        return [];
      }

      console.log('🔍 [ChatService] Fetching chat sessions for user:', user.id);
      
      const { data: chatSessions, error } = await supabase
        .from('chat_sessions')
        .select('*')
        .eq('user_id', user.id)
        .order('updated_at', { ascending: false });

      if (error) {
        console.error('❌ [ChatService] Error fetching chat sessions:', error);
        throw error;
      }

      console.log('🔍 [ChatService] Raw chat sessions:', chatSessions);

      const convertedSessions: ChatSession[] = (chatSessions || []).map((session) => ({
        id: session.id,
        title: session.title,
        messages: [], // Load messages lazily
        lastActivity: new Date(session.updated_at),
        iconName: session.icon_name || 'SpeechBubbleIcon',
        assistantId: session.assistant_id,
        assistantName: session.assistant_name,
        assistantEmoji: session.assistant_emoji
      }));

      console.log('🔍 [ChatService] Converted chat sessions:', convertedSessions);
      return convertedSessions;
    }, 'getChatSessions');
    
    return result || [];
  }

  /**
   * Get messages for a specific chat session
   */
  async getMessagesForSession(sessionId: string): Promise<Message[]> {
    const result = await connectionService.executeWithRetry(async () => {
      console.log('🔍 [ChatService] getMessagesForSession called for:', sessionId);
      
      // Ensure auth session is valid
      const hasValidSession = await connectionService.ensureAuthSession();
      if (!hasValidSession) {
        console.log('🔍 [ChatService] No valid authenticated session');
        return [];
      }
      
      const { data: messages, error } = await supabase
        .from('messages')
        .select('*')
        .eq('chat_session_id', sessionId)
        .order('timestamp', { ascending: true });

      if (error) {
        console.error('❌ [ChatService] Error fetching messages:', error);
        throw error;
      }

      console.log('🔍 [ChatService] Raw messages:', messages);

      const convertedMessages: Message[] = (messages || []).map(msg => ({
        id: msg.id,
        text: msg.content,
        sender: msg.sender_type as 'user' | 'ai',
        timestamp: new Date(msg.timestamp),
        avatar: msg.avatar_url || undefined,
        chatId: sessionId,
        isLoading: msg.is_loading || false,
        isError: msg.is_error || false,
        groundingChunks: msg.grounding_chunks || []
      }));

      console.log('🔍 [ChatService] Converted messages:', convertedMessages);
      return convertedMessages;
    }, `getMessagesForSession(${sessionId})`);
    
    return result || [];
  }

  /**
   * Create a new chat session with optional assistant context
   */
  async createChatSession(
    title: string,
    iconName: string = 'SpeechBubbleIcon',
    assistantId?: string | null,
    assistantName?: string | null,
    assistantEmoji?: string | null
  ): Promise<ChatSession | null> {
    return connectionService.executeWithRetry(async () => {
      console.log('🔍 [ChatService] createChatSession called:', {
        title,
        iconName,
        assistantId,
        assistantName,
        assistantEmoji
      });

      // Ensure auth session is valid
      const hasValidSession = await connectionService.ensureAuthSession();
      if (!hasValidSession) {
        console.log('🔍 [ChatService] No valid authenticated session');
        return null;
      }

      const { data: { session } } = await supabase.auth.getSession();
      const user = session?.user;

      if (!user) {
        console.error('❌ [ChatService] No authenticated user for chat creation');
        return null;
      }

      const { data: newSession, error } = await supabase
        .from('chat_sessions')
        .insert({
          user_id: user.id,
          title,
          icon_name: iconName,
          assistant_id: assistantId,
          assistant_name: assistantName,
          assistant_emoji: assistantEmoji,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        console.error('❌ [ChatService] Error creating chat session:', error);
        throw error;
      }

      console.log('🔍 [ChatService] Chat session created:', newSession);

      return {
        id: newSession.id,
        title: newSession.title,
        messages: [],
        lastActivity: new Date(newSession.created_at),
        iconName: newSession.icon_name,
        assistantId: newSession.assistant_id,
        assistantName: newSession.assistant_name,
        assistantEmoji: newSession.assistant_emoji
      };
    }, 'createChatSession');
  }

  /**
   * Add a message to a chat session
   */
  async addMessage(chatSessionId: string, message: Omit<Message, 'id' | 'timestamp'>): Promise<Message | null> {
    console.log('🔍 [ChatService] addMessage called:', { 
      chatSessionId, 
      messageText: message.text?.substring(0, 50), 
      sender: message.sender,
      isLoading: message.isLoading
    });
    
    // Don't save messages that are still loading
    if (message.isLoading) {
      console.log('⚠️ [ChatService] Skipping save for loading message');
      return null;
    }
    
    const messageData = {
      chat_session_id: chatSessionId,
      content: message.text,
      sender_type: message.sender,
      metadata: {},
      is_loading: false, // Always save as not loading
      is_error: message.isError || false,
      avatar_url: message.avatar || null,
      grounding_chunks: message.groundingChunks || [],
      timestamp: new Date().toISOString()
    };

    // Use connection service to ensure reliable database operation
    return await connectionService.executeWithRetry(async () => {
      // Ensure auth session is valid
      const hasValidSession = await connectionService.ensureAuthSession();
      if (!hasValidSession) {
        console.log('🔍 [ChatService] No valid authenticated session for addMessage');
        return null;
      }

      const { data: newMessage, error } = await supabase
        .from('messages')
        .insert(messageData)
        .select()
        .single();

      if (error) {
        console.error('❌ [ChatService] Error adding message:', error);
        throw error;
      }

      console.log('✅ [ChatService] Message added successfully:', newMessage.id);

      // Update chat session's updated_at timestamp
      await supabase
        .from('chat_sessions')
        .update({ updated_at: new Date().toISOString() })
        .eq('id', chatSessionId);

      return {
        id: newMessage.id,
        text: newMessage.content,
        sender: newMessage.sender_type as 'user' | 'ai',
        timestamp: new Date(newMessage.timestamp),
        avatar: newMessage.avatar_url || undefined,
        isLoading: newMessage.is_loading,
        isError: newMessage.is_error,
        groundingChunks: newMessage.grounding_chunks || [],
        chatId: chatSessionId
      };
    }, 'addMessage');
  }

  /**
   * Update a chat session's title
   */
  async updateChatTitle(sessionId: string, title: string): Promise<boolean> {
    try {
      console.log('🔍 [ChatService] updateChatTitle called:', { sessionId, title });
      
      const { error } = await supabase
        .from('chat_sessions')
        .update({ 
          title,
          updated_at: new Date().toISOString()
        })
        .eq('id', sessionId);

      if (error) {
        console.error('❌ [ChatService] Error updating chat title:', error);
        return false;
      }

      console.log('✅ [ChatService] Chat title updated successfully');
      return true;
    } catch (error) {
      console.error('❌ [ChatService] updateChatTitle error:', error);
      return false;
    }
  }

  /**
   * Delete a chat session and all its messages
   */
  async deleteChatSession(sessionId: string): Promise<boolean> {
    try {
      console.log('🔍 [ChatService] deleteChatSession called:', sessionId);
      
      // First, verify the chat belongs to the current user
      const { data: { session } } = await supabase.auth.getSession();
      const user = session?.user;
      
      if (!user) {
        console.error('❌ [ChatService] No authenticated user for chat deletion');
        return false;
      }

      // Check if chat belongs to user
      const { data: chatSession, error: checkError } = await supabase
        .from('chat_sessions')
        .select('user_id')
        .eq('id', sessionId)
        .single();

      if (checkError || !chatSession) {
        console.error('❌ [ChatService] Chat session not found:', checkError);
        return false;
      }

      if (chatSession.user_id !== user.id) {
        console.error('❌ [ChatService] Access denied - chat does not belong to user');
        return false;
      }

      // Delete all messages first (cascade delete)
      const { error: messagesError } = await supabase
        .from('messages')
        .delete()
        .eq('chat_session_id', sessionId);

      if (messagesError) {
        console.error('❌ [ChatService] Error deleting messages:', messagesError);
        return false;
      }

      console.log('✅ [ChatService] Messages deleted for chat:', sessionId);

      // Then delete the chat session
      const { error: sessionError } = await supabase
        .from('chat_sessions')
        .delete()
        .eq('id', sessionId);

      if (sessionError) {
        console.error('❌ [ChatService] Error deleting chat session:', sessionError);
        return false;
      }

      console.log('✅ [ChatService] Chat session deleted successfully:', sessionId);
      return true;
    } catch (error) {
      console.error('❌ [ChatService] deleteChatSession error:', error);
      return false;
    }
  }

  /**
   * Delete all chats for a specific user (used when deleting user account)
   */
  async deleteAllUserChats(userId: string): Promise<boolean> {
    try {
      console.log('🔍 [ChatService] deleteAllUserChats called for user:', userId);
      
      // Get all chat sessions for the user
      const { data: userChats, error: fetchError } = await supabase
        .from('chat_sessions')
        .select('id')
        .eq('user_id', userId);

      if (fetchError) {
        console.error('❌ [ChatService] Error fetching user chats:', fetchError);
        return false;
      }

      if (!userChats || userChats.length === 0) {
        console.log('🔍 [ChatService] No chats found for user:', userId);
        return true;
      }

      const chatIds = userChats.map(chat => chat.id);
      console.log('🔍 [ChatService] Deleting chats:', chatIds);

      // Delete all messages for these chats
      const { error: messagesError } = await supabase
        .from('messages')
        .delete()
        .in('chat_session_id', chatIds);

      if (messagesError) {
        console.error('❌ [ChatService] Error deleting user messages:', messagesError);
        return false;
      }

      console.log('✅ [ChatService] Messages deleted for user chats');

      // Delete all chat sessions for the user
      const { error: sessionsError } = await supabase
        .from('chat_sessions')
        .delete()
        .eq('user_id', userId);

      if (sessionsError) {
        console.error('❌ [ChatService] Error deleting user chat sessions:', sessionsError);
        return false;
      }

      console.log('✅ [ChatService] All chats deleted for user:', userId);
      return true;
    } catch (error) {
      console.error('❌ [ChatService] deleteAllUserChats error:', error);
      return false;
    }
  }
}

// Export singleton instance
export const chatService = ChatService.getInstance();