--- MainArea.tsx.original
+++ MainArea.tsx.fixed
@@ -1801,7 +1801,8 @@
 const ModelDropdown: React.FC<{
   selectedModel: Model;
   onModelChange: (modelId: string) => void;
-}> = ({ selectedModel, onModelChange }) => {
+  isSidebarOpen: boolean;
+}> = ({ selectedModel, onModelChange, isSidebarOpen }) => {
   const [isOpen, setIsOpen] = useState(false);
   const dropdownRef = useRef<HTMLDivElement>(null);
 
@@ -1840,7 +1841,10 @@
       {isOpen && (
         <div
-          className="absolute left-0 mt-2 w-56 bg-white dark:bg-slate-700 rounded-md shadow-lg ring-1 ring-black dark:ring-slate-600 ring-opacity-5 z-20 py-1"
+          className={`absolute mt-2 w-56 bg-white dark:bg-slate-700 rounded-md shadow-lg ring-1 ring-black dark:ring-slate-600 ring-opacity-5 z-20 py-1 ${
+            // When sidebar is open, position dropdown to the right to avoid overlap
+            isSidebarOpen ? 'right-0' : 'left-0'
+          }`}
           role="menu"
           aria-orientation="vertical"
           aria-labelledby="model-dropdown-button"
@@ -1870,6 +1874,7 @@
         <ModelDropdown
           selectedModel={selectedModel}
           onModelChange={onModelChange}
+          isSidebarOpen={isSidebarOpen}
           data-oid="ej.ej.."
         />
       </div>
