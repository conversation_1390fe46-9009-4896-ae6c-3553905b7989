<!-- Use this file to provide workspace-specific custom instructions to <PERSON>pi<PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

This is a Node.js backend project using Express. Follow best practices for REST API development and keep endpoints modular.

# Project Key Details

- Express server runs on port 3001 by default.
- Endpoints:
  - `GET /` — Returns 'Welcome to the backend API!'
  - `GET /api/health` — Returns `{ status: 'ok' }` for health checks.
- All dependencies managed via npm (`express` installed).
- VS Code tasks configured for easy server start.
- Copilot instructions and README present.

# Next Steps
- Explore frontend code to align backend API with frontend needs.
- Design and implement additional API endpoints (e.g., chat, users).
- Consider database integration if persistent storage is needed.
- Implement CORS, error handling, and authentication as required.

# Note
- Each Copilot session is stateless. Copy these details into your next prompt to continue seamlessly.
