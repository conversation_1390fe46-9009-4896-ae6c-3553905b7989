---
inclusion: always
---

# Modular Architecture Standards

## Core Principle: Prevent Feature Regression

When adding new features or modifying existing ones, always follow these architectural principles to prevent breaking existing functionality.

## Service Architecture Rules

### 1. Feature Isolation
- **Create dedicated services** for each major feature domain (Chat, User Management, Settings, etc.)
- **Never mix concerns** - ChatService should only handle chat operations, UserService only user operations
- **No cross-dependencies** between feature services (ChatService should not import UserService directly)

### 2. Service Structure
```typescript
// ✅ Correct: Dedicated service with single responsibility
export class ChatService {
  private static instance: ChatService;
  
  static getInstance(): ChatService {
    if (!ChatService.instance) {
      ChatService.instance = new ChatService();
    }
    return ChatService.instance;
  }
  
  // Only chat-related methods here
  async getChatSessions(): Promise<ChatSession[]> { ... }
  async createChatSession(title: string): Promise<ChatSession | null> { ... }
  async addMessage(chatId: string, message: Message): Promise<Message | null> { ... }
}
```

### 3. Coordinator Pattern
- **Use DataService as a coordinator** that delegates to specialized services
- **Maintain backward compatibility** by keeping existing method signatures
- **Add comprehensive logging** for debugging and monitoring

```typescript
// ✅ Correct: DataService delegates to specialized services
async getChatSessions(): Promise<ChatSession[]> {
  try {
    console.log('🔍 [DataService] Delegating to ChatService.getChatSessions');
    return await chatService.getChatSessions();
  } catch (error) {
    console.error('❌ [DataService] getChatSessions error:', error);
    return [];
  }
}
```

### 4. Error Handling & Logging
- **Always add comprehensive logging** with service prefixes: `[ServiceName]`
- **Use consistent log levels**: 🔍 for info, ❌ for errors, ✅ for success
- **Graceful degradation** - return empty arrays/null instead of throwing errors
- **Fallback mechanisms** where appropriate

### 5. Database Access Patterns
- **Direct Supabase calls** in specialized services (no supabaseHelpers dependency)
- **Consistent error handling** across all database operations
- **Proper data transformation** from database format to application format

## Development Workflow

### Before Adding New Features:
1. **Identify the feature domain** (Chat, User, Settings, etc.)
2. **Check if a dedicated service exists** for this domain
3. **Create a new service if needed** following the singleton pattern
4. **Update DataService** to delegate to the new service
5. **Add comprehensive logging** throughout

### Before Modifying Existing Features:
1. **Identify which service handles** the functionality
2. **Make changes only in the appropriate service**
3. **Test both the modified feature AND related features** to prevent regression
4. **Check console logs** to ensure proper delegation flow

### Testing Requirements:
- **Test the specific feature** you're working on
- **Test at least one other major feature** to ensure no regression
- **Check console logs** for proper service delegation
- **Verify error handling** works as expected

## Anti-Patterns to Avoid

### ❌ Don't Do This:
```typescript
// Mixing concerns in one service
class DataService {
  async getChatSessions() { /* chat logic */ }
  async getAllUsers() { /* user logic */ }
  async updateSettings() { /* settings logic */ }
}

// Direct cross-service dependencies
import { userService } from './userService';
class ChatService {
  async createChat() {
    const user = await userService.getCurrentUser(); // ❌ Wrong!
  }
}
```

### ✅ Do This Instead:
```typescript
// Separated concerns
class ChatService {
  async getChatSessions() { /* only chat logic */ }
}

class UserService {
  async getAllUsers() { /* only user logic */ }
}

// Coordination through DataService
class DataService {
  async getChatSessions() {
    return await chatService.getChatSessions();
  }
  
  async getAllUsers() {
    return await userService.getAllUsers();
  }
}
```

## File Organization

```
frontend/services/
├── dataService.ts          # Coordinator service
├── chatService.ts          # Chat-specific operations
├── userService.ts          # User management operations
├── settingsService.ts      # Settings operations (future)
└── notificationService.ts  # Notifications (future)
```

## Success Metrics

A well-architected feature should:
- ✅ Work independently without affecting other features
- ✅ Have clear, comprehensive logging
- ✅ Handle errors gracefully
- ✅ Be easily testable in isolation
- ✅ Follow the single responsibility principle

## When in Doubt

If you're unsure about architecture decisions:
1. **Create a new dedicated service** rather than adding to existing ones
2. **Prefer isolation over convenience**
3. **Add more logging rather than less**
4. **Test multiple features** after any change
5. **Ask: "Could this change break something else?"**

Remember: **It's better to have too many small, focused services than one large, complex service.**