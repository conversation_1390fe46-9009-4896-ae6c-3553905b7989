import path from 'path';
import { defineConfig } from 'vite';

export default defineConfig({
  // Environment variables with VITE_ prefix are automatically available
  // No need for manual define since we're using import.meta.env.VITE_*
  resolve: {
    alias: {
      '@': path.resolve(__dirname, '.'),
    }
  },
  // Optional: Configure environment variable loading
  envPrefix: 'VITE_',
  server: {
    port: 5173, // Match the port in your Supabase redirect URL
    host: true
  }
});
