{"name": "gemini-ai-chat-interface", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "translation:check": "node scripts/translation-sync.js check", "translation:sync": "node scripts/translation-sync.js sync", "translation:add": "node scripts/translation-sync.js add"}, "dependencies": {"@emoji-mart/react": "^1.1.1", "@google/genai": "^1.6.0", "@supabase/supabase-js": "^2.50.5", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "@types/react-syntax-highlighter": "^15.5.13", "dotenv": "^17.2.1", "i18next": "^23.12.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i": "^0.2.1", "react-i18next": "^15.0.0", "react-markdown": "^10.1.0", "react-router-dom": "^6.22.3", "react-syntax-highlighter": "^15.6.1", "remark-gfm": "^4.0.1"}, "devDependencies": {"@types/node": "^22.14.0", "@types/react-dom": "^18.2.7", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "typescript": "~5.7.2", "vite": "^6.2.0"}}